//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2025/4/11.
//

#include "convert_android.h"

#include <flatbuffers/flatbuffers.h>
#include <fstream>
#include "Command_generated.h"
#include "generated/TouchEvent_generated.h"
#include "generated/ComponentEvent_generated.h"
#include "style_convertor_helper.h"
#include "types.h"
#include "../../../../Source/Public/types_def.h"

using namespace com::meituan::android::msc::renderer::generated;

namespace blink::mt {
    template<typename T>
    bool shouldUpdate(bool isFirst, T oldValue, T newValue, T defaultValue) {
        if (isFirst && !(newValue == defaultValue)) {
            return true;
        }
        if (!isFirst && !(oldValue == newValue)) {
            return true;
        }
        return false;
    }

    typedef enum TransformKey {
        Opacity,
        Color,
        BackgroundColor,
        OverflowX,
        OverflowY,
        BorderTopStyle,
        BorderLeftStyle,
        BorderBottomStyle,
        BorderRightStyle,
        BorderTopWidth,
        BorderLeftWidth,
        BorderBottomWidth,
        BorderRightWidth,
        BorderTopColor,
        BorderLeftColor,
        BorderBottomColor,
        BorderRightColor,
        BorderTopLeftRadius,
        BorderBottomLeftRadius,
        BorderTopRightRadius,
        BorderBottomRightRadius,
        BackgroundImage,
        BackgroundSize,
        BackgroundRepeat,
        PointerEvents,
        BoxShadow,
        Transform,
        FontSize,
        FontFamily,
        FontStyle,
        FontWeight,
        TextAlign,
        VerticalAlign,
        NumberOfLines,
        EllipsizeMode,
        TextDecorationLine,
        LetterSpacing,
        WordSpacing,
        TextOverflow,
        WhiteSpace,
        WordBreak,
        BackdropFilter,
        TextPadding
    };


    flatbuffers::Offset<DisplayItem> getItemResult(flatbuffers::FlatBufferBuilder& builder, std::shared_ptr<const NativeStyle> oldItem, std::shared_ptr<const NativeStyle> newItem, float density, bool isMsi) {
//                if (isPropertyChanged2<DisplayInfo>(old_result, new_result, EOverflow::kVisible,
//                                                    &DisplayInfo::OverflowX)) {
//                    itemJson["overflowX"] = StyleConvertor::Convert(newStyle->OverflowX());
//                }
        bool isFirst = oldItem == nullptr;
        if (oldItem == nullptr) {
            oldItem = std::make_shared<NativeStyle>();
        }

        // 预处理string，string会修改内存结构
//        auto viewName = builder.CreateString(name);
        std::vector<int> changeTags;
        if (shouldUpdate(isFirst, oldItem->opacity, newItem->opacity, 1.0f)) {
            changeTags.push_back(TransformKey::Opacity);
        }
        if (shouldUpdate(isFirst, oldItem->color, newItem->color, newItem->default_color)) {
            changeTags.push_back(TransformKey::Color);
        }
        if (shouldUpdate(isFirst, oldItem->background_color, newItem->background_color, Color::kTransparent)) {
            changeTags.push_back(TransformKey::BackgroundColor);
        }
        flatbuffers::Offset<flatbuffers::String> overflowX = 0;
        if (shouldUpdate(isFirst, oldItem->overflow_x, newItem->overflow_x, EOverflow::kVisible)) {
            changeTags.push_back(TransformKey::OverflowX);
            overflowX = builder.CreateString(StyleConvertor::Convert(newItem->overflow_x));
        }
        flatbuffers::Offset<flatbuffers::String> overflowY = 0;
        if (shouldUpdate(isFirst, oldItem->overflow_y, newItem->overflow_y, EOverflow::kVisible)) {
            changeTags.push_back(TransformKey::OverflowY);
            overflowY = builder.CreateString(StyleConvertor::Convert(newItem->overflow_y));
        }
        // backgroundImage
        flatbuffers::Offset<::flatbuffers::Vector<flatbuffers::Offset<PropArray>>> backgroundImageVectorBuf = StyleConvertor::ConvertBackgroundImage(builder, newItem->background_image, newItem->background_gradient);
        if (!backgroundImageVectorBuf.IsNull()) {
            changeTags.push_back(TransformKey::BackgroundImage);
        }
        flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<PropArray>>> backgroundSizeVectorBuf = StyleConvertor::ConvertBackgroundSize(builder, newItem->background_size, density);
        if (!backgroundSizeVectorBuf.IsNull()) {
            changeTags.push_back(TransformKey::BackgroundSize);
        }
        // backgroundRepeat
        flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<StringArray>>> backgroundRepeatVectorBuf = 0;
        if (newItem->background_repeat_x != EFillRepeat::kRepeatFill &&
            newItem->background_repeat_y != EFillRepeat::kRepeatFill) {
            changeTags.push_back(TransformKey::BackgroundRepeat);
            flatbuffers::Offset<flatbuffers::String> repeatXStr = builder.CreateString(StyleConvertor::Convert(newItem->background_repeat_x));
            flatbuffers::Offset<flatbuffers::String> repeatYStr = builder.CreateString(StyleConvertor::Convert(newItem->background_repeat_y));
            std::vector<flatbuffers::Offset<flatbuffers::String>> backgroundRepeatArray = {repeatXStr, repeatYStr};
            flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> vals = builder.CreateVector(backgroundRepeatArray);
            flatbuffers::Offset<StringArray> stringArr = CreateStringArray(builder, vals);
            backgroundRepeatVectorBuf = builder.CreateVector({stringArr});
        }
        // border相关
        bool isBorderTopFirstValid = isFirst && newItem->border_top_style != EBorderStyle::kNone;
        bool isBorderLeftFirstValid = isFirst && newItem->border_left_style != EBorderStyle::kNone;
        bool isBorderBottomFirstValid = isFirst && newItem->border_bottom_style != EBorderStyle::kNone;
        bool isBorderRightFirstValid = isFirst && newItem->border_right_style != EBorderStyle::kNone;
        flatbuffers::Offset<flatbuffers::String> borderTopStyle = 0;
        if (isBorderTopFirstValid || shouldUpdate(isFirst, oldItem->border_top_style, newItem->border_top_style, EBorderStyle::kNone)) {
            changeTags.push_back(TransformKey::BorderTopStyle);
            borderTopStyle = builder.CreateString(StyleConvertor::Convert(newItem->border_top_style));
        }
        flatbuffers::Offset<flatbuffers::String> borderLeftStyle = 0;
        if(isBorderLeftFirstValid || shouldUpdate(isFirst, oldItem->border_left_style, newItem->border_left_style, EBorderStyle::kNone)) {
            changeTags.push_back(TransformKey::BorderLeftStyle);
            borderLeftStyle = builder.CreateString(StyleConvertor::Convert(newItem->border_left_style));
        }
        flatbuffers::Offset<flatbuffers::String> borderBottomStyle = 0;
        if(isBorderBottomFirstValid || shouldUpdate(isFirst, oldItem->border_bottom_style, newItem->border_bottom_style, EBorderStyle::kNone)) {
            changeTags.push_back(TransformKey::BorderBottomStyle);
            borderBottomStyle = builder.CreateString(StyleConvertor::Convert(newItem->border_bottom_style));
        }
        flatbuffers::Offset<flatbuffers::String> borderRightStyle = 0;
        if(isBorderRightFirstValid || shouldUpdate(isFirst, oldItem->border_right_style, newItem->border_right_style, EBorderStyle::kNone)){
            changeTags.push_back(TransformKey::BorderRightStyle);
            borderRightStyle = builder.CreateString(StyleConvertor::Convert(newItem->border_right_style));
        }
        double defaultBorderTopWidth = ComputedStyle::BorderWidth(newItem->border_top_style, 3);
        double defaultBorderLeftWidth = ComputedStyle::BorderWidth(newItem->border_left_style, 3);
        double defaultBorderBottomWidth = ComputedStyle::BorderWidth(newItem->border_bottom_style, 3);
        double defaultBorderRightWidth = ComputedStyle::BorderWidth(newItem->border_right_style, 3);
        blink::Color defaultBorderColor = newItem->default_border_color;
        if(isBorderTopFirstValid || shouldUpdate(isFirst, oldItem->border_top_width, newItem->border_top_width, defaultBorderTopWidth)){
            changeTags.push_back(TransformKey::BorderTopWidth);
        }
        if(isBorderTopFirstValid || shouldUpdate(isFirst, oldItem->border_top_color, newItem->border_top_color, defaultBorderColor)){
            changeTags.push_back(TransformKey::BorderTopColor);
        }
        if(isBorderLeftFirstValid || shouldUpdate(isFirst, oldItem->border_left_width, newItem->border_left_width, defaultBorderLeftWidth)){
            changeTags.push_back(TransformKey::BorderLeftWidth);
        }
        if(isBorderLeftFirstValid || shouldUpdate(isFirst, oldItem->border_left_color, newItem->border_left_color, defaultBorderColor)){
            changeTags.push_back(TransformKey::BorderLeftColor);
        }
        if(isBorderBottomFirstValid || shouldUpdate(isFirst, oldItem->border_bottom_width, newItem->border_bottom_width, defaultBorderBottomWidth)){
            changeTags.push_back(TransformKey::BorderBottomWidth);
        }
        if(isBorderBottomFirstValid || shouldUpdate(isFirst, oldItem->border_bottom_color, newItem->border_bottom_color, defaultBorderColor)){
            changeTags.push_back(TransformKey::BorderBottomColor);
        }
        if(isBorderRightFirstValid || shouldUpdate(isFirst, oldItem->border_right_width, newItem->border_right_width, defaultBorderRightWidth)){
            changeTags.push_back(TransformKey::BorderRightWidth);
        }
        if(isBorderRightFirstValid || shouldUpdate(isFirst, oldItem->border_right_color, newItem->border_right_color, defaultBorderColor)){
            changeTags.push_back(TransformKey::BorderRightColor);
        }
        flatbuffers::Offset<flatbuffers::String> pointerEvents = 0;
        if (shouldUpdate(isFirst, oldItem->pointer_events, newItem->pointer_events, EPointerEvents::kAuto)) {
            changeTags.push_back(TransformKey::PointerEvents);
            pointerEvents = builder.CreateString(StyleConvertor::Convert(newItem->pointer_events));
        }
        LengthSize defaultSize = LengthSize(Length::Fixed(0), Length::Fixed(0));
        if (shouldUpdate(isFirst, oldItem->border_top_left_radius, newItem->border_top_left_radius, defaultSize)) {
            changeTags.push_back(TransformKey::BorderTopLeftRadius);
        }
        if (shouldUpdate(isFirst, oldItem->border_top_right_radius, newItem->border_top_right_radius, defaultSize)) {
            changeTags.push_back(TransformKey::BorderTopRightRadius);
        }
        if (shouldUpdate(isFirst, oldItem->border_bottom_right_radius, newItem->border_bottom_right_radius, defaultSize)) {
            changeTags.push_back(TransformKey::BorderBottomRightRadius);
        }
        if (shouldUpdate(isFirst, oldItem->border_bottom_left_radius, newItem->border_bottom_left_radius, defaultSize)) {
            changeTags.push_back(TransformKey::BorderBottomLeftRadius);
        }
        if (newItem->is_text && (shouldUpdate(isFirst, oldItem->text_padding_left, newItem->text_padding_left, std::float_t(0))
            || shouldUpdate(isFirst, oldItem->text_padding_top, newItem->text_padding_top, std::float_t(0))
            || shouldUpdate(isFirst, oldItem->text_padding_right, newItem->text_padding_right, std::float_t(0))
            || shouldUpdate(isFirst, oldItem->text_padding_bottom, newItem->text_padding_bottom, std::float_t(0)))) {
            changeTags.push_back(TransformKey::TextPadding);
        }
        // box-shadow
        flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Prop>>> boxShadowVectorBuf = StyleConvertor::ConvertFbs(builder, newItem->box_shadow, newItem->shadow_color_list);
        if (!boxShadowVectorBuf.IsNull()) {
            changeTags.push_back(TransformKey::BoxShadow);
        }
        // 文本相关信息
        flatbuffers::Offset<flatbuffers::String> fontStyleBuf = 0;
        flatbuffers::Offset<flatbuffers::String> textAlignBuf = 0;
        if (isMsi) {
            if (shouldUpdate(isFirst, oldItem->font_style, newItem->font_style, std::string("normal"))) {
                changeTags.push_back(TransformKey::FontStyle);
                fontStyleBuf = builder.CreateString(newItem->font_style);
            }
            changeTags.push_back(TransformKey::FontSize);
            if (shouldUpdate(isFirst, oldItem->text_align, newItem->text_align, ETextAlign::kStart)) {
                changeTags.push_back(TransformKey::TextAlign);
                textAlignBuf = builder.CreateString(StyleConvertor::Convert(newItem->text_align));
            }
        }
        if (changeTags.empty()) {
            return 0;
        }
        auto int_vector = builder.CreateVector(changeTags);
        DisplayItemBuilder displayItemBuilder(builder);
//        displayItemBuilder.add_tag(tag);
//        displayItemBuilder.add_container_tag(container_tag);
//        displayItemBuilder.add_view_name(viewName);
        displayItemBuilder.add_change_keys(int_vector);

        for (int key : changeTags) {
            switch (key) {
                case TransformKey::Opacity:
                    displayItemBuilder.add_opacity(newItem->opacity);
                    break;
                case TransformKey::Color:
                    displayItemBuilder.add_color((int) StyleConvertor::ConvertColorToInt32(newItem->color));
                    break;
                case TransformKey::BackgroundColor:
                    displayItemBuilder.add_background_color((int) StyleConvertor::ConvertColorToInt32(newItem->background_color));
                    break;
                case TransformKey::OverflowX:
                    displayItemBuilder.add_overflow_x(overflowX);
                    break;
                case TransformKey::OverflowY:
                    displayItemBuilder.add_overflow_y(overflowY);
                    break;
                case TransformKey::BorderTopStyle:
                    displayItemBuilder.add_border_top_style(borderTopStyle);
                    break;
                case TransformKey::BorderLeftStyle:
                    displayItemBuilder.add_border_left_style(borderLeftStyle);
                    break;
                case TransformKey::BorderBottomStyle:
                    displayItemBuilder.add_border_bottom_style(borderBottomStyle);
                    break;
                case TransformKey::BorderRightStyle:
                    displayItemBuilder.add_border_right_style(borderRightStyle);
                    break;
                case TransformKey::BorderTopWidth:
                    displayItemBuilder.add_border_top_width(newItem->border_top_width * density);
                    break;
                case TransformKey::BorderLeftWidth:
                    displayItemBuilder.add_border_left_width(newItem->border_left_width * density);
                    break;
                case TransformKey::BorderBottomWidth:
                    displayItemBuilder.add_border_bottom_width(newItem->border_bottom_width * density);
                    break;
                case TransformKey::BorderRightWidth:
                    displayItemBuilder.add_border_right_width(newItem->border_right_width * density);
                    break;
                case TransformKey::BorderTopColor:
                    displayItemBuilder.add_border_top_color((int) StyleConvertor::ConvertColorToInt32(newItem->border_top_color));
                    break;
                case TransformKey::BorderLeftColor:
                    displayItemBuilder.add_border_left_color((int) StyleConvertor::ConvertColorToInt32(newItem->border_left_color));
                    break;
                case TransformKey::BorderBottomColor:
                    displayItemBuilder.add_border_bottom_color((int) StyleConvertor::ConvertColorToInt32(newItem->border_bottom_color));
                    break;
                case TransformKey::BorderRightColor:
                    displayItemBuilder.add_border_right_color((int) StyleConvertor::ConvertColorToInt32(newItem->border_right_color));
                    break;
                case TransformKey::BorderTopLeftRadius:
                    displayItemBuilder.add_border_top_left_radius(StyleConvertor::ConvertBorderRadius(newItem->border_top_left_radius) * density);
                    break;
                case TransformKey::BorderTopRightRadius:
                    displayItemBuilder.add_border_top_right_radius(StyleConvertor::ConvertBorderRadius(newItem->border_top_right_radius) * density);
                    break;
                case TransformKey::BorderBottomRightRadius:
                    displayItemBuilder.add_border_bottom_right_radius(StyleConvertor::ConvertBorderRadius(newItem->border_bottom_right_radius) * density);
                    break;
                case TransformKey::BorderBottomLeftRadius:
                    displayItemBuilder.add_border_bottom_left_radius(StyleConvertor::ConvertBorderRadius(newItem->border_bottom_left_radius) * density);
                    break;
                case TransformKey::PointerEvents:
                    displayItemBuilder.add_pointer_events(pointerEvents);
                    break;
                case TransformKey::BackgroundImage:
                    displayItemBuilder.add_background_image(backgroundImageVectorBuf);
                    break;
                case TransformKey::BackgroundSize:
                    displayItemBuilder.add_background_size(backgroundSizeVectorBuf);
                    break;
                case TransformKey::BackgroundRepeat:
                    displayItemBuilder.add_background_repeat(backgroundRepeatVectorBuf);
                    break;
                case TransformKey::BoxShadow:
                    displayItemBuilder.add_box_shadow(boxShadowVectorBuf);
                    break;
                case TransformKey::FontStyle:
                    displayItemBuilder.add_font_style(fontStyleBuf);
                    break;
                case TransformKey::FontSize:
                    displayItemBuilder.add_font_size(newItem->font_size * density);
                    break;
                case TransformKey::TextAlign:
                    displayItemBuilder.add_text_align(textAlignBuf);
                    break;
                case TransformKey::TextPadding:
                    std::vector<float> textPadding;
                    textPadding.push_back(newItem->text_padding_left * density);
                    textPadding.push_back(newItem->text_padding_top * density);
                    textPadding.push_back(newItem->text_padding_right * density);
                    textPadding.push_back(newItem->text_padding_bottom * density);
                    displayItemBuilder.add_text_padding(builder.CreateVector(textPadding));
                    break;
            }
        }
        return displayItemBuilder.Finish();
    }


    flatbuffers::Offset<Prop> convertProp(flatbuffers::FlatBufferBuilder& builder, const std::string &key, const PropValue& val) {
        flatbuffers::Offset<Prop> prop_offset;
        auto key_offset = builder.CreateString(key);
        if (val.isA<PropValueType::String>()) {
            auto prop_str_offset = builder.CreateString(val.stringValue());
            prop_offset = CreateProp(builder, key_offset,
                                     com::meituan::android::msc::renderer::generated::Value_StringValue,
                                     CreateStringValue(builder, prop_str_offset).Union());
        } else if (val.isA<PropValueType::Number>()) {
            prop_offset = CreateProp(builder, key_offset,
                                     com::meituan::android::msc::renderer::generated::Value_FloatValue,
                                     CreateFloatValue(builder, val.numberValue()).Union());
        } else if (val.isA<PropValueType::Array>()) {
            // TODO : create 和 update目前还没有array类型的
        } else if (val.isA<PropValueType::Dictionary>()) {
            // TODO : create 和 update目前还没有array类型的
        }
        return prop_offset;
    }

    flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<Prop>>> convertProps(flatbuffers::FlatBufferBuilder &builder, std::shared_ptr<const Props> props) {
        std::vector<flatbuffers::Offset<Prop>> props_vector;
        props->forEach([&props_vector, &builder](const std::string &key, const PropValue &val) {
            flatbuffers::Offset<Prop> prop_offset = convertProp(builder, key, val);
            if (!prop_offset.IsNull()) {
                props_vector.push_back(prop_offset);
            }
        });
        return builder.CreateVector(props_vector);
    }

    std::pair<uint8_t*, size_t> GetResult(flatbuffers::FlatBufferBuilder& builder, const blink::mt::UICommands &buffer, float density,
                                          function<std::shared_ptr<const NativeStyle>(int)> oldDisplayInfoGetter,
                                          function<void(int, std::shared_ptr<const NativeStyle>)> oldDisplayInfoSetter) {
        std::vector<flatbuffers::Offset<Command>> result;
        for (const auto &command: *buffer) {
            UICommandType type = command->type;
            switch (type) {
                case UICommandType::CreateView: {
                    auto *createViewCommand = static_cast<CreateViewCommamnd *>(command.get());
                    auto tag = createViewCommand->tag;
                    auto view_name_offset = builder.CreateString(createViewCommand->view_name);
                    auto props_offset = convertProps(builder, createViewCommand->props);
                    auto create_view_command = CreateCreateViewCommand(builder, view_name_offset, props_offset);
                    auto cmd = CreateCommand(builder, createViewCommand->id, tag, CommandValue_CreateViewCommand, create_view_command.Union());
                    result.push_back(cmd);
                    break;
                }
                case UICommandType::UpdateView: {
                    auto updateViewCommand = static_cast<UpdateViewCommamnd *>(command.get());
                    auto view_name_offset = builder.CreateString(updateViewCommand->view_name);
                    auto props_offset = convertProps(builder, updateViewCommand->props);
                    auto update_view_command = CreateUpdateViewCommand(builder, view_name_offset, props_offset);
                    auto cmd = CreateCommand(builder, updateViewCommand->id, updateViewCommand->tag, CommandValue_UpdateViewCommand, update_view_command.Union());
                    result.push_back(cmd);
                    break;
                }
                case UICommandType::DeleteView: {
                    auto deleteViewCommamnd = static_cast<DeleteViewsCommamnd *>(command.get());
                    flatbuffers::Offset<::flatbuffers::Vector<int32_t>> delete_ops_buf = builder.CreateVector(deleteViewCommamnd->delete_ops);
                    auto deleteViewCommandBuf = CreateDeleteViewsCommand(builder, delete_ops_buf);
                    auto cmd = CreateCommand(builder, deleteViewCommamnd->id, deleteViewCommamnd->tag, CommandValue_DeleteViewsCommand, deleteViewCommandBuf.Union());
                    result.push_back(cmd);
                    break;
                }
                case UICommandType::InsertChildViews: {
                    auto insertChildViewsCommand = static_cast<InsertChildViewsCommand *>(command.get());
                    auto insert_ops = insertChildViewsCommand->insert_ops;
                    std::vector<flatbuffers::Offset<ViewAtIndex>> insert_ops_vec;
                    for (auto op: insert_ops) {
                        flatbuffers::Offset<ViewAtIndex> viewAtIndexBuf = CreateViewAtIndex(builder, op.tag, op.index);
                        insert_ops_vec.push_back(viewAtIndexBuf);
                    }
                    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<ViewAtIndex>>> insert_ops_vec_buf = 0;
                    if (!insert_ops_vec.empty()) {
                        insert_ops_vec_buf = builder.CreateVector(insert_ops_vec);
                    }
                    auto insertChildViewCommandBuf = CreateInsertChildViewsCommand(builder, insert_ops_vec_buf);
                    auto cmd = CreateCommand(builder, insertChildViewsCommand->id,
                                             insertChildViewsCommand->tag,
                                             CommandValue_InsertChildViewsCommand,
                                             insertChildViewCommandBuf.Union());
                    result.push_back(cmd);
                    break;
                }
                case UICommandType::RemoveChildViews: {
                    auto removeChildViewsCommand = static_cast<RemoveChildViewsCommand *>(command.get());
                    auto remove_ops = removeChildViewsCommand->remove_ops;
                    std::vector<int> remove_ops_tags;
                    for (auto op: remove_ops) {
                        remove_ops_tags.push_back(op);
                    }
                    flatbuffers::Offset<flatbuffers::Vector<int>> remove_ops_tags_buf = 0;
                    if (!remove_ops_tags.empty()) {
                        remove_ops_tags_buf = builder.CreateVector(remove_ops_tags);
                    }
                    auto removeChildViewCommandBuf = CreateRemoveChildViewsCommand(builder, remove_ops_tags_buf);
                    auto cmd = CreateCommand(builder, removeChildViewsCommand->id,
                                             removeChildViewsCommand->tag,
                                             CommandValue_RemoveChildViewsCommand,
                                             removeChildViewCommandBuf.Union());
                    result.push_back(cmd);
                    break;
                }
                case UICommandType::UpdateViewStyle: {
                    auto updateViewStyleCommamnd = static_cast<blink::mt::UpdateViewStyleCommamnd *>(command.get());
                    std::shared_ptr<const NativeStyle> oldStyle = oldDisplayInfoGetter(updateViewStyleCommamnd->tag);
                    shared_ptr<const NativeStyle> style = updateViewStyleCommamnd->style;
                    flatbuffers::Offset<DisplayItem> displayItemBuf = getItemResult(builder, oldStyle, style, density, style->is_msi);
                    oldDisplayInfoSetter(updateViewStyleCommamnd->tag, style);
                    if (!displayItemBuf.IsNull()) {
                        auto updateViewStyleCommandBuf = CreateUpdateViewStyleCommamnd(builder, displayItemBuf);
                        auto cmd = CreateCommand(builder, updateViewStyleCommamnd->id, updateViewStyleCommamnd->tag, CommandValue_UpdateViewStyleCommamnd, updateViewStyleCommandBuf.Union());
                        result.push_back(cmd);
                    }
                    break;
                }
                case UICommandType::UpdateViewFrame: {
                    auto updateViewFrameCommamnd = static_cast<UpdateViewFrameCommamnd *>(command.get());
                    auto updateViewFrameCommandBuf = CreateUpdateViewFrameCommand(builder, 0, updateViewFrameCommamnd->x * density,
                                                                                  updateViewFrameCommamnd->y * density,
                                                                                  updateViewFrameCommamnd->width * density,
                                                                                  updateViewFrameCommamnd->height * density);
                    auto cmd = CreateCommand(builder, updateViewFrameCommamnd->id, updateViewFrameCommamnd->tag, CommandValue_UpdateViewFrameCommand, updateViewFrameCommandBuf.Union());
                    result.push_back(cmd);
                    break;
                }
                case UICommandType::UpdateViewTransform:{
                    auto updateViewTransformCommamnd = static_cast<UpdateViewTransformCommamnd *>(command.get());
                    NativeTransforms transforms = updateViewTransformCommamnd->transforms;
                    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Prop>>> transformsBuf = mt::StyleConvertor::ConvertFbs(builder, transforms, density);
                    if (!transformsBuf.IsNull()) {
                        auto updateViewTransformCommandBuf = CreateUpdateTransformCommand(builder, transformsBuf);
                        auto cmd = CreateCommand(builder, updateViewTransformCommamnd->id, updateViewTransformCommamnd->tag, CommandValue_UpdateTransformCommand, updateViewTransformCommandBuf.Union());
                        result.push_back(cmd);
                    }
                    break;
                }
                case UICommandType::BatchDidFinish: {
                    auto batchDidFinishCommand = static_cast<BatchDidFinishCommand *>(command.get());
                    // 将 LayoutReason 枚举转换为 FlatBuffers 的 LayoutReason 枚举
                    com::meituan::android::msc::renderer::generated::LayoutReason fbsLayoutReason;
                    switch (batchDidFinishCommand->reason) {
                        case LayoutReason::BatchDidComplete:
                            fbsLayoutReason = com::meituan::android::msc::renderer::generated::LayoutReason_BatchDidComplete;
                        break;
                        case LayoutReason::PreBatchDidComplete:
                            fbsLayoutReason = com::meituan::android::msc::renderer::generated::LayoutReason_PreBatchDidComplete;
                        break;
                        case LayoutReason::WXSSetStyle:
                            fbsLayoutReason = com::meituan::android::msc::renderer::generated::LayoutReason_WXSSetStyle;
                        break;
                        default:
                            fbsLayoutReason = com::meituan::android::msc::renderer::generated::LayoutReason_BatchDidComplete;
                        break;
                    }
                    auto batchDidFinishCommandBuf = CreateBatchDidFinishCommand(builder, fbsLayoutReason);
                    auto cmd = CreateCommand(builder, batchDidFinishCommand->id, batchDidFinishCommand->tag, CommandValue_BatchDidFinishCommand, batchDidFinishCommandBuf.Union());
                    result.push_back(cmd);
                    break;
                }
                case UICommandType::UpdateText: {
                    auto updateTextCommand = static_cast<UpdateTextCommamnd *>(command.get());
                    auto updateTextCommandBuf = CreateUpdateTextCommamnd(builder, updateTextCommand->x * density, updateTextCommand->y * density, updateTextCommand->width * density, updateTextCommand->height * density,
                                                                         updateTextCommand->top_inset * density, updateTextCommand->left_inset * density, updateTextCommand->right_inset * density, updateTextCommand->bottom_inset * density);
                    auto cmd = CreateCommand(builder, updateTextCommand->id, updateTextCommand->tag, CommandValue_UpdateTextCommamnd, updateTextCommandBuf.Union());
                    result.push_back(cmd);
                    break;
                }
                case UICommandType::CreateKeyframesAnimation: {
                  auto createkeyframesAnimationCommand =
                      static_cast<CreateKeyframesAnimationCommand *>(
                          command.get());
                  std::vector<int> tags_array;
                  std::transform(
                      std::begin(createkeyframesAnimationCommand->tags),
                      std::end(createkeyframesAnimationCommand->tags),
                      std::back_insert_iterator(tags_array),
                      [](auto v) { return static_cast<int>(v.numberValue()); });
                  auto tags = builder.CreateVector<int32_t>(tags_array);

                  std::vector<::flatbuffers::Offset<
                      com::meituan::android::msc::renderer::generated::
                          CreateAnimationKeyFrame>>
                      keyframes_vec;
                  for (auto &kf_value :
                       createkeyframesAnimationCommand->keyframes) {
                    auto &kf = kf_value.dictionaryValue();

                    ::flatbuffers::Optional<float> offset =
                        ::flatbuffers::nullopt;
                    if (kf.count("offset") > 0) {
                      offset = kf.at("offset").numberValue();
                    }

                    flatbuffers::Offset<flatbuffers::String> ease = 0;
                    if (kf.count("ease") > 0) {
                      ease = builder.CreateString(
                          kf.at("ease").stringValue().c_str());
                    }

                    flatbuffers::Optional<float> opacity =
                        ::flatbuffers::nullopt;
                    if (kf.count("opacity") > 0) {
                      opacity.emplace(kf.at("opacity").numberValue());
                    }

                    ::flatbuffers::Optional<float> rotate =
                        ::flatbuffers::nullopt;
                    if (kf.count("rotate") > 0) {
                      rotate = kf.at("rotate").numberValue();
                    }

                    ::flatbuffers::Offset<::flatbuffers::Vector<float>> scale =
                        0;
                    if (kf.count("scale") > 0) {
                      std::vector<float> scale_vec;
                      for (auto &v : kf.at("scale").arrayValue()) {
                        scale_vec.push_back(v.numberValue());
                      }
                      scale = builder.CreateVector<float>(scale_vec);
                    }

                    ::flatbuffers::Offset<::flatbuffers::Vector<float>>
                        translate = 0;
                    if (kf.count("translate") > 0) {
                      std::vector<float> translate_vec;
                      for (auto &v : kf.at("translate").arrayValue()) {
                        translate_vec.push_back(v.numberValue());
                      }
                      translate = builder.CreateVector<float>(translate_vec);
                    }
                    ::flatbuffers::Optional<float> width =
                        ::flatbuffers::nullopt;
                    ::flatbuffers::Offset<::flatbuffers::String> width_str = 0;
                    if (kf.count("width") > 0) {
                      auto value = kf.at("width");
                      if (value.isA<PropValueType::Number>()) {
                        width = value.numberValue();
                      } else if (value.isA<PropValueType::String>()) {
                        width_str = builder.CreateString(value.stringValue());
                      }
                    }

                    auto key_frame = CreateCreateAnimationKeyFrame(
                        builder, offset, ease, opacity, rotate, scale,
                        translate, width, width_str);

                    keyframes_vec.push_back(key_frame);
                  }

                  auto keyframes = builder.CreateVector<::flatbuffers::Offset<
                      com::meituan::android::msc::renderer::generated::
                          CreateAnimationKeyFrame>>(keyframes_vec);

                  auto callback = new std::function<void()>(
                      createkeyframesAnimationCommand->callback);

                  auto createkeyframesAnimationCommandBuf =
                      CreateCreateKeyframesAnimationCommand(
                          builder, tags, keyframes,
                          createkeyframesAnimationCommand->duration,
                          reinterpret_cast<uint64_t>(callback));

                  auto cmd = CreateCommand(
                      builder, createkeyframesAnimationCommand->id,
                      createkeyframesAnimationCommand->tag,
                      CommandValue_CreateKeyframesAnimationCommand,
                      createkeyframesAnimationCommandBuf.Union());

                  result.push_back(cmd);

                  break;
                }
                case UICommandType::ClearKeyframesAnimation: {
                  auto clearkeyframesAnimationCommand =
                      static_cast<ClearKeyframesAnimationCommand *>(
                          command.get());
                  std::vector<int> tags_array;
                  std::transform(
                      std::begin(clearkeyframesAnimationCommand->tags),
                      std::end(clearkeyframesAnimationCommand->tags),
                      std::back_insert_iterator(tags_array),
                      [](auto v) { return static_cast<int>(v.numberValue()); });
                  auto tags = builder.CreateVector<int32_t>(tags_array);

                  ::flatbuffers::Offset<com::meituan::android::msc::renderer::
                                            generated::ClearAnimationOptions>
                      options = 0;
                  if (!clearkeyframesAnimationCommand->options.isNull() &&
                      clearkeyframesAnimationCommand->options
                          .isA<PropValueType::Dictionary>()) {
                    auto &dict = clearkeyframesAnimationCommand->options
                                     .dictionaryValue();
                    ::flatbuffers::Optional<bool> opacity =
                        ::flatbuffers::nullopt;
                    if (dict.count("opacity") > 0) {
                      opacity = dict.at("opacity").boolValue();
                    }

                    ::flatbuffers::Optional<bool> scale =
                        ::flatbuffers::nullopt;
                    if (dict.count("scale") > 0) {
                      scale = dict.at("scale").boolValue();
                    }

                    ::flatbuffers::Optional<bool> translate =
                        ::flatbuffers::nullopt;
                    if (dict.count("translate") > 0) {
                      translate = dict.at("translate").boolValue();
                    }

                    ::flatbuffers::Optional<bool> rotate =
                        ::flatbuffers::nullopt;
                    if (dict.count("rotate") > 0) {
                      rotate = dict.at("rotate").boolValue();
                    }
                    options = CreateClearAnimationOptions(
                        builder, opacity, scale, translate, rotate);
                  }

                  auto callback = new std::function<void()>(
                      clearkeyframesAnimationCommand->callback);
                  auto clearkeyframesAnimationCommandBuf =
                      CreateClearKeyframesAnimationCommand(
                          builder, tags, options,
                          reinterpret_cast<uint64_t>(callback));

                  auto cmd =
                      CreateCommand(builder, clearkeyframesAnimationCommand->id,
                                    clearkeyframesAnimationCommand->tag,
                                    CommandValue_ClearKeyframesAnimationCommand,
                                    clearkeyframesAnimationCommandBuf.Union());
                  result.push_back(cmd);
                  break;
                }
            }
        }
        flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<Command>>> commandArray  = builder.CreateVector(result);
        flatbuffers::Offset<CommandArray>  array =   CreateCommandArray(builder, commandArray);
        builder.Finish(array);
        uint8_t* buffer_ptr = builder.GetBufferPointer();  // 内存起始地址
        size_t buffer_size = builder.GetSize();
        return std::make_pair(buffer_ptr, buffer_size);
    }

std::shared_ptr<msc::native_dom::EventData> constructTouchEventFromBytes(const uint8_t *buf, int* ele_id) {
  auto* fbsEvent = flatbuffers::GetRoot<FbsTouchEvent>(buf);
  auto touchEvent = std::make_shared<msc::native_dom::EventData>(msc::native_dom::EventData::EventCategory::TOUCH);
  std::string event_name = flatbuffers::GetString(fbsEvent->eventName());
  touchEvent->SetEventName(event_name);
  LOGI("NativeDOM touchEvent name=%s", touchEvent->GetEventName().c_str());
  float pageX;
  float pageY;
  for(size_t i = 0; i < fbsEvent->touches()->size(); i++) {
    auto fbsTouch = fbsEvent->touches()->Get(i);
    *ele_id = fbsTouch->target();
    pageX = fbsTouch->pageX();
    pageY = fbsTouch->pageY();
    msc::native_dom::Touch touch(fbsTouch->touchId(), fbsTouch->locationX(), fbsTouch->locationY(),
                                 pageX, pageY, nullptr, false);
    touchEvent->GetTouches().push_back(touch);
    LOGI("touchEvent name=%s, id=%d", touchEvent->GetEventName().c_str(), fbsTouch->touchId());
    if (fbsTouch->changed()) {
      touchEvent->GetChangedTouches().push_back(touch);
    }
  }
  if (touchEvent->GetEventName() == "long_press" || touchEvent->GetEventName() == "tap") {
    blink::mt::PropsBuilder builder;
    builder.setProp("x", pageX);
    builder.setProp("y", pageY);
    touchEvent->SetDetail(builder.getProps());
  }
  return touchEvent;
}

std::shared_ptr<msc::native_dom::EventData> constructComponentEventFromBytes(const uint8_t *buf, int* ele_id) {
        auto* fbs_event = flatbuffers::GetRoot<ComponentEvent>(buf);
        auto event = std::make_shared<msc::native_dom::EventData>(msc::native_dom::EventData::EventCategory::COMPONENT);
        if (!fbs_event->props()) {
            return nullptr;
        }
        event->SetEventName(flatbuffers::GetString(fbs_event->event_name()));
        *ele_id = fbs_event->target();
        blink::mt::PropsBuilder builder;
        for(size_t i = 0; i < fbs_event->props()->size(); i++) {
            auto prop = fbs_event->props()->Get(i);
            std::string key = prop->key()->c_str();
            if (key.empty()) {
                continue;
            }
            switch(prop->value_type_type()) {
              case Value::Value_IntValue: {
                  int intValue = static_cast<const com::meituan::android::msc::renderer::generated::IntValue *>(prop->val())->val();
                  builder.setProp(key, (double)intValue);
                  break;
              }
              case Value::Value_FloatValue: {
                  float floatValue = static_cast<const com::meituan::android::msc::renderer::generated::FloatValue *>(prop->val())->val();
                  builder.setProp(key, floatValue);
                  break;
              }
              case Value::Value_StringValue: {
                  std::string stringValue = static_cast<const com::meituan::android::msc::renderer::generated::StringValue *>(prop->val())->val()->c_str();
                  builder.setProp(key, stringValue);
                  break;
              }
            }
        }
        event->SetDetail(builder.getProps());
        return event;
    }
}