//
// Created by 李挺 on 2020-02-19.
//

#ifndef MTV8_MTV8_H
#define MTV8_MTV8_H

#include <memory>

#define MTV8_EXPORT __attribute__ ((visibility("default")))

namespace mtv8 {
class MTV8Member;
class JSRuntime;

class JSValue;
class JSName;
class JSBoolean;
class JSNumber;
class JSInteger;
class JSObject;
class JSString;
class JSArray;
class JSSymbol;
class JSFunction;

class JSErrorMessage;

struct JSObjectTemplate;

class JSHeapStatistics;

class MTV8_EXPORT JSValueVisitor {
public:
    virtual void VisitKey(char* key, int length) {};
    virtual void VisitNumberValue(double value) {};
    virtual void VisitBooleanValue(bool value) {};
    virtual void VisitNullValue() {};
    virtual void VisitUndefinedValue() {};
    virtual void VisitStringValue(uint16_t* value, int length) {};
    virtual void VisitObjectStart() {};
    virtual void VisitObjectEnd() {};
    virtual void VisitArrayStart(int length) {};
    virtual void VisitArrayEnd() {};
};

enum MTV8_EXPORT BufferPolicy {
    BufferNotOwned,
    BufferOwned
};

class MTV8_EXPORT JSRuntimeStartupData {
public:
    const char* data;
    int raw_size;
    BufferPolicy buffer_policy = BufferOwned;

    ~JSRuntimeStartupData() {
        if (buffer_policy == BufferOwned) {
            delete[] data;
        }
    }
};

struct MTV8_EXPORT JSCodeCachedData {

    // If buffer_policy is BufferNotOwned, the caller keeps the ownership of
    // data and guarantees that it stays alive until the CachedData object is
    // destroyed. If the policy is BufferOwned, the given data will be deleted
    // (with delete[]) when the CachedData object is destroyed.
    JSCodeCachedData(const uint8_t* data_, int length_,
               BufferPolicy buffer_policy_ = BufferNotOwned) : data(data_),
                                                             length(length_),
                                                             rejected(false),
                                                             buffer_policy(buffer_policy_) {}
    ~JSCodeCachedData() {
        if (buffer_policy == BufferOwned) {
            delete[] data;
        }
    }
    const uint8_t* data;
    int length;
    bool rejected;
    BufferPolicy buffer_policy;

    /**
      * Return a version tag for CachedData for the current V8 version & flags.
      *
      * This value is meant only for determining whether a previously generated
      * CachedData instance is still valid; the tag has no other meaing.
      *
      * Background: The data carried by CachedData may depend on the exact
      *   V8 version number or current compiler flags. This means that when
      *   persisting CachedData, the embedder must take care to not pass in
      *   data from another V8 version, or the same version with different
      *   features enabled.
      *
      *   The easiest way to do so is to clear the embedder's cache on any
      *   such change.
      *
      *   Alternatively, this tag can be stored alongside the cached data and
      *   compared when it is being used.
      */
    static uint32_t CachedDataVersionTag();

    // Prevent copying.
    JSCodeCachedData(const JSCodeCachedData&) = delete;
    JSCodeCachedData& operator=(const JSCodeCachedData&) = delete;
};

typedef std::shared_ptr<JSValue> (*JSFunctionCallback)
        (JSRuntime* runtime, std::shared_ptr<JSObject> recv,
                std::shared_ptr<JSArray> args, uint32_t argc, void* extraData);

/**
* JS运行时的实例，负责对象的生成、执行JS代码、JSON处理和一些别的事情
*/
class MTV8_EXPORT JSRuntime {
public:
    JSRuntime();

    ~JSRuntime();

    static JSRuntime* InitFromStartupData(std::unique_ptr<JSRuntimeStartupData>& newStartupData);

    static JSRuntime* OnlyForCreateStartupData();

    std::unique_ptr<JSRuntimeStartupData> CreateStartupData();

    std::shared_ptr<JSValue> GetUndefined();

    std::shared_ptr<JSValue> GetNull();

    std::shared_ptr<JSBoolean> NewBoolean(bool value);

    std::shared_ptr<JSNumber> NewNumber(double value);

    std::shared_ptr<JSInteger> NewInteger(int32_t value);

    std::shared_ptr<JSObject> NewObject();

    /**
     * 使用JSObject模版来生成一个JSObject
     */
    std::shared_ptr<JSObject> NewObjectFromTemplate(std::shared_ptr<JSObjectTemplate> objectTemplate, void* extraData);

    std::shared_ptr<JSString> NewFromOneByte(const uint8_t* data, int length = -1);

    std::shared_ptr<JSString> NewStringFromUtf8(const char* data, int length = -1);

    std::shared_ptr<JSString> NewStringFromUtf16(const uint16_t* data, int length = -1);

    std::shared_ptr<JSArray> NewArray(size_t length = 0);

    std::shared_ptr<JSSymbol> NewSymbol(std::shared_ptr<JSString> name = std::shared_ptr<JSString>());

    std::shared_ptr<JSFunction> NewFunction(JSFunctionCallback callback, void* extraData);

    /**
     * 全局对象
     */
    std::shared_ptr<JSObject> GetGlobalObject();

    /**
       * Schedules an exception to be thrown when returning to JavaScript.  When an
       * exception has been scheduled it is illegal to invoke any JavaScript
       * operation; the caller must return immediately and only after the exception
       * has been handled does it become legal to invoke JavaScript operations.
       */
    std::shared_ptr<JSValue> ThrowException(std::shared_ptr<JSValue> exception);

    std::shared_ptr<JSValue> ExecuteScript(std::shared_ptr<JSString> source, std::shared_ptr<JSString> sourceName,
                                           std::shared_ptr<JSErrorMessage>& errMsg);

    std::shared_ptr<JSValue> ExecuteScriptFromCachedData(std::shared_ptr<JSString> source, std::shared_ptr<JSString> sourceName,
                                           std::unique_ptr<JSCodeCachedData>& cachedData,std::shared_ptr<JSErrorMessage>& errMsg);

    std::unique_ptr<JSCodeCachedData> CreateCachedData(std::shared_ptr<JSString> source,
                                                             std::shared_ptr<JSString> sourceName,
                                                             std::shared_ptr<JSErrorMessage> &errMsg);

    std::shared_ptr<JSValue> JSONParse(std::shared_ptr<JSString> json_string);

    std::shared_ptr<JSString> JSONStringify(std::shared_ptr<JSValue> json_object);

    /**
     * 释放js对象的引用，直到这个js对象被销毁才会执行WeakCallback，
     * 注意：调用SetWeak后，如果value值继续被持有，回调就不会被调用
     * shared_ptr 的 use_count必须为1
     */
    void SetWeak(std::shared_ptr<JSValue> value, void* p, void(*callback)(JSRuntime*, void*));

    JSRuntime(const JSRuntime&) = delete;
    void operator=(const JSRuntime&) = delete;

    //void RequestGarbageCollection();

    /**主动Garbagecollet,可以在堆内存不足时使用，其他情况应尽量减少使用,SetWeak的回调函数中禁止使用
     * 注意：调用该函数可能会增加耗时，影响性能；也可能影响MTV8GC的时间表
     */
    void LowMemoryNotification();

    void SetFlagsFromString(const char* str);

    void GetHeapStatistics(JSHeapStatistics& js_heap_statistics);

    std::shared_ptr<JSString> GetV8Version();

    void VisitJSValue(std::shared_ptr<JSValue> value, std::shared_ptr<JSValueVisitor> visiter);

    bool WriteHeapSnapshot(std::ostream& os);

    void StartCPUProfiling(const std::string& profilerName, int interval = 1000);

    bool StopCPUProfiling(const std::string& profilerName, std::ostream& os);

    void InitInspectorAndCallback(std::string inspectName, std::function<void (std::function<void ()>)> func);

    void ChangeInspectorName(std::string newName);

private:
    JSRuntime(std::unique_ptr<MTV8Member>);
    std::unique_ptr<MTV8Member> p_member;
    friend class MTV8Member;
};

/**
* JS对象的基类
*/
class MTV8_EXPORT JSValue {
public:
    JSValue();

    ~JSValue();

    /**
       * Returns true if this value is the undefined value.  See ECMA-262
       * 4.3.10.
       */
    bool IsUndefined() const;

    /**
     * Returns true if this value is the null value.  See ECMA-262
     * 4.3.11.
     */
    bool IsNull() const;

    /**
   * Returns true if this value is either the null or the undefined value.
   * See ECMA-262
   * 4.3.11. and 4.3.12
   */
    bool IsNullOrUndefined() const {
        return IsUndefined() || IsNull();
    };

    /**
     * Returns true if this value is a symbol or a string.
     */
    bool IsName() const {
        return IsSymbol() || IsString();
    };

    /**
     * Returns true if this value is an instance of the String type.
     * See ECMA-262 8.4.
     */
    bool IsString() const;

    /**
     * Returns true if this value is a symbol.
     */
    bool IsSymbol() const;

    /**
     * Returns true if this value is a function.
     */
    bool IsFunction() const;

    /**
     * Returns true if this value is an array. Note that it will return false for
     * an Proxy for an array.
     */
    bool IsArray() const;

    /**
     * Returns true if this value is an object.
     */
    bool IsObject() const;

    /**
     * Returns true if this value is boolean.
     */
    bool IsBoolean() const;

    /**
     * Returns true if this value is a integer.
     */
    bool IsInteger() const;

    /**
     * Returns true if this value is a number.
     */
    bool IsNumber() const;

    /**
     * 比较2个JS对象是否宽松相等 相当于JS中的 ==
     */
    bool Equals(std::shared_ptr<JSValue> that) const;
    /**
     * 比较2个JS对象是否严格相等 相当于JS中的 ===
     */
    bool StrictEquals(std::shared_ptr<JSValue> that) const;
    /**
     * 同值相等
     */
    bool SameValue(std::shared_ptr<JSValue> that) const;

//    std::shared_ptr<JSString> TypeOf();

    bool InstanceOf(std::shared_ptr<JSObject> object);

protected:
    std::unique_ptr<MTV8Member> p_member;
    friend class MTV8Member;
};

class MTV8_EXPORT JSBoolean : public JSValue {
public:
    bool BooleanValue() const;

    static std::shared_ptr<JSBoolean> Cast(std::shared_ptr<JSValue>  obj);
};

class MTV8_EXPORT JSNumber : public JSValue {
public:
    double NumberValue() const;

    static std::shared_ptr<JSNumber> Cast(std::shared_ptr<JSValue>  obj);
};

class MTV8_EXPORT JSInteger : public JSNumber {
public:
    int32_t IntValue() const;

    static std::shared_ptr<JSInteger> Cast(std::shared_ptr<JSValue>  obj);
};

class MTV8_EXPORT JSObject : public JSValue {
public:
    bool Set(std::shared_ptr<JSName> key, std::shared_ptr<JSValue> value);
    std::shared_ptr<JSValue> Get(std::shared_ptr<JSName> key);
    bool Has(std::shared_ptr<JSName> key) const;
    bool Delete(std::shared_ptr<JSName> key);

    bool Set(uint32_t index, std::shared_ptr<JSValue> value);
    std::shared_ptr<JSValue> Get(uint32_t index);
    bool Has(uint32_t index) const;
    bool Delete(uint32_t index);

    /**
   * Returns an array containing the names of the enumerable properties
   * of this object, including properties from prototype objects.  The
   * array returned by this method contains the same values as would
   * be enumerated by a for-in statement over this object.
   */
    std::shared_ptr<JSArray> GetPropertyNames() const ;

    /**
   * Checks whether a callback is set by the
   * ObjectTemplate::SetCallAsFunctionHandler method.
   * When an Object is callable this method returns true.
   */
    bool IsCallable();

    /**
     * True if this object is a constructor.
     */
    bool IsConstructor();

    static std::shared_ptr<JSObject> Cast(std::shared_ptr<JSValue>  obj);
};

/**
 * A superclass for symbols and strings.
 */
class MTV8_EXPORT JSName : public JSValue {
public:
    static std::shared_ptr<JSName> Cast(std::shared_ptr<JSValue>  obj);
};

class MTV8_EXPORT JSString : public JSName {
public:
    /**
     * Returns the number of characters (UTF-16 code units) in this string.
     */
    uint32_t Utf16Length() const;

    /**
     * Returns the number of bytes in the UTF-8 encoded
     * representation of this string.
     */
    uint32_t Utf8Length() const;

    /**
     * Returns whether this string is known to contain only one byte data,
     * i.e. ISO-8859-1 code points.
     * Does not read the string.
     * False negatives are possible.
     */
    bool IsOneByte() const;

    char* Utf8Value();

    uint16_t* Utf16Value();

    bool StringEquals(std::shared_ptr<JSString> str);

    static std::shared_ptr<JSString> Cast(std::shared_ptr<JSValue>  obj);
};

class MTV8_EXPORT JSArray : public JSObject {
public:
    uint32_t Length() const;

    static std::shared_ptr<JSArray> Cast(std::shared_ptr<JSValue>  obj);
};

class MTV8_EXPORT JSSymbol : public JSName {
public:
    /**
     * Returns the print name string of the symbol, or undefined if none.
     */
    std::shared_ptr<JSValue> Name() const;

    static std::shared_ptr<JSSymbol> Cast(std::shared_ptr<JSValue>  obj);
};

class MTV8_EXPORT JSFunction : public JSObject {
public:
    std::shared_ptr<JSObject> NewInstance(std::shared_ptr<JSArray> args, uint32_t argc) const;

    std::shared_ptr<JSValue> Call(std::shared_ptr<JSValue> recv, std::shared_ptr<JSArray> args, uint32_t argc, std::shared_ptr<JSErrorMessage>& errMsg);

    static std::shared_ptr<JSFunction> Cast(std::shared_ptr<JSValue>  obj);
};

/**
 * An error message.
 */
class MTV8_EXPORT JSErrorMessage {
public:
    enum ErrorType {CompileError, ExecuteError};

    JSErrorMessage(ErrorType type,
                    std::shared_ptr<JSString> exception,
                    std::shared_ptr<JSString> stackTrace = std::shared_ptr<JSString>(),
                    std::shared_ptr<JSString> sourceLine = std::shared_ptr<JSString>(),
                    std::shared_ptr<JSString> sourceFileName = std::shared_ptr<JSString>(),
                    int lineNumber = 0,
                    int startPosition = 0,
                    int endPosition = 0,
                    int startColumn = 0,
                    int endColumn = 0) :
                    type_(type),
                    exception_(exception),
                    stackTrace_(stackTrace),
                    sourceLine_(sourceLine),
                    sourceFileName_(sourceFileName),
                    lineNumber_(lineNumber),
                    startPosition_(startPosition),
                    endPosition_(endPosition),
                    startColumn_(startColumn),
                    endColumn_(endColumn) {

    }

    /**
     * 错误分2种：执行错误 ExecuteError 和 编译错误 CompileError
     */
    ErrorType GetType() const {
        return type_;
    }

    std::shared_ptr<JSString> GetException() const {
        return exception_;
    };

    std::shared_ptr<JSString> GetSourceLine() const {
        return sourceLine_;
    };

    /**
     * Returns the resource name for the script from where the function causing
     * the error originates.
     */
    std::shared_ptr<JSString> GetSourceFileName() const {
        return sourceFileName_;
    }

    std::shared_ptr<JSString> GetStackTrace() const {
        return stackTrace_;
    }

    /**
     * Returns the number, 1-based, of the line where the error occurred.
     */
    int GetLineNumber() const {
        return lineNumber_;
    }

    /**
     * Returns the index within the script of the first character where
     * the error occurred.
     */
    int GetStartPosition() const {
        return startPosition_;
    }

    /**
     * Returns the index within the script of the last character where
     * the error occurred.
     */
    int GetEndPosition() const {
        return endPosition_;
    }

    /**
     * Returns the index within the line of the first character where
     * the error occurred.
     */
    int GetStartColumn() const {
        return startColumn_;
    }

    /**
     * Returns the index within the line of the last character where
     * the error occurred.
     */
    int GetEndColumn() const {
        return endColumn_;
    }

private:
    ErrorType type_;
    std::shared_ptr<JSString> exception_;
    std::shared_ptr<JSString> stackTrace_;
    std::shared_ptr<JSString> sourceLine_;
    std::shared_ptr<JSString> sourceFileName_;
    int lineNumber_;
    int startPosition_;
    int endPosition_;
    int startColumn_;
    int endColumn_;
};


struct MTV8_EXPORT JSObjectTemplate {
    // Object api
    std::shared_ptr<JSValue> (*GetNamedProperty)(JSRuntime* runtime,
            std::shared_ptr<JSName> property, void* extraData);

    void (*SetNamedProperty)(JSRuntime* runtime,
            std::shared_ptr<JSName> property, std::shared_ptr<JSValue> value, void* extraData);

    std::shared_ptr<JSArray> (*GetNamedProperties)(JSRuntime* runtime, void* extraData);

    bool (*DeleteNamedProperty)(JSRuntime* runtime,
            std::shared_ptr<JSName> property, void* extraData);

    // Array api
    std::shared_ptr<JSValue> (*GetIndexedProperty)(JSRuntime* runtime,
            uint32_t index, void* extraData);

    void (*SetIndexedProperty)(JSRuntime* runtime,
            uint32_t index, std::shared_ptr<JSValue> value, void* extraData);

    std::shared_ptr<JSArray> (*GetIndexedProperties)(JSRuntime* runtime, void* extraData);

    bool (*DeleteIndexedProperty)(JSRuntime* runtime,
            uint32_t index, void* extraData);

    //Function api，将object作为方法使用的时候会调用这个方法
    JSFunctionCallback callback;
};

class MTV8_EXPORT JSHeapStatistics {
public:
  JSHeapStatistics();

  size_t TotalHeapSize() { return total_heap_size_; }
  size_t TotalHeapSizeExecutable() { return total_heap_size_executable_; }
  size_t TotalPhysicalSize() { return total_physical_size_; }
  size_t TotalAvailableSize() { return total_available_size_; }
  size_t UsedHeapSize() { return used_heap_size_; }
  size_t HeapSizeLimit() { return heap_size_limit_; }
  size_t MallocedMemory() { return malloced_memory_; }
  size_t ExternalMemory() { return external_memory_; }
  size_t PeakMallocedMemory() { return peak_malloced_memory_; }
  size_t NumberOfNativeContext() { return number_of_native_contexts_; }
  size_t NumberOfDetachedContext() { return number_of_detached_contexts_; }
  bool DoesZapGarbage() { return does_zap_garbage_; }

private:
  size_t total_heap_size_;
  size_t total_heap_size_executable_;
  size_t total_physical_size_;
  size_t total_available_size_;
  size_t used_heap_size_;
  size_t heap_size_limit_;
  size_t malloced_memory_;
  size_t external_memory_;
  size_t peak_malloced_memory_;
  size_t number_of_native_contexts_;
  size_t number_of_detached_contexts_;
  bool does_zap_garbage_;

  friend class MTV8Member;
};


} // end namespace mtv8


#endif //MTV8_MTV8_H