//
// Created by lilei118 on 2025/2/7.
//

#include "system_trace.h"

#if defined(__ANDROID__)
#include <android/api-level.h>
#include <dlfcn.h>
#endif

#include <mutex>

namespace blink {

namespace atrace {

// 全局变量定义
std::once_flag flag;

// 在 ndk 中已经导出到了 <android/trace.h>，但是需要 minSDK 23 以上
// 在内部使用的时候，直接动态导入，绕开 __ANDROID_API__ >= 23 限制
// https://cs.android.com/android/platform/superproject/main/+/main:external/oboe/src/common/Trace.cpp
// https://groups.google.com/g/android-ndk/c/HYslKGuhHZc
void* (*ATrace_beginSection)(const char* sectionName) = nullptr;
void* (*ATrace_endSection)(void) = nullptr;
bool* (*ATrace_isEnabled)(void) = nullptr;

void InitTrace() {
#if defined(__ANDROID__)
  int api_level = android_get_device_api_level();
  if (api_level < 23) {
    return;
  }
  // Native Trace API is supported in API level 23
  void* lib = dlopen("libandroid.so", RTLD_NOW | RTLD_LOCAL);
  if (lib != NULL) {
    // LOGI("Run with Trace Native API.");
    //  Retrieve function pointers from shared object.
    ATrace_beginSection = reinterpret_cast<fp_ATrace_beginSection>(
        dlsym(lib, "ATrace_beginSection"));
    ATrace_endSection =
        reinterpret_cast<fp_ATrace_endSection>(dlsym(lib, "ATrace_endSection"));
    ATrace_isEnabled =
        reinterpret_cast<fp_ATrace_isEnabled>(dlsym(lib, "ATrace_isEnabled"));
  }
#endif
}

void BeginTrace(const char* sectionName) {
#if defined(__ANDROID__)
  // 动态加载/初始化
  std::call_once(flag, InitTrace);
  if (ATrace_isEnabled && ATrace_beginSection) {
    if (ATrace_isEnabled()) {
      ATrace_beginSection(sectionName);
    }
  }
#endif
}

void EndTrace() {
#if defined(__ANDROID__)
  std::call_once(flag, InitTrace);
  if (ATrace_isEnabled && ATrace_endSection) {
    if (ATrace_isEnabled()) {
      ATrace_endSection();
    }
  }
#endif
}

}  // namespace atrace

}  // namespace blink