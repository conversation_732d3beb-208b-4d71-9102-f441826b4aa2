//
// Created by <PERSON><PERSON><PERSON> on 2025/3/17.
//

#ifndef RENDERER_TRACEUTILS_H
#define RENDERER_TRACEUTILS_H

#define MSC_ENABLE_NEW_TRACE 1
#include "system_trace.h"

#if MSC_ENABLE_NEW_TRACE
#define MSC_TRACE_SCOPED_EVENT(name) ScopedTraceEvent _traceEvent##__COUNTER__(#name);
#define MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(name) ScopedTraceEvent _traceEvent##__COUNTER__("ndom_"#name);
#else
#define MSC_TRACE_SCOPED_EVENT(name)
#define MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(name)
#endif

#ifndef __APPLE__
struct TraceEvent {
    /**
     * begin 1; end 2; instance 3; duration 4
     */
    int type;
    const char *name;
    long time;
    long unixTs;
    long duration;
};


void traceBegin(const char *sectionName);

void traceEnd(const char *sectionName);

void instance(const char *sectionName);

void duration(const char *sectionName, long duration);

void getAllEvents(std::vector<std::string> &result);

class ScopedTraceEvent {
public:
    ScopedTraceEvent(const char *name) {
        blink::atrace::BeginTrace(name);
    }
    ~ScopedTraceEvent() {
        blink::atrace::EndTrace();
    }
};
#endif


#endif //RENDERER_TRACEUTILS_H
