//
//  mtdocument.m
//  MSCRenderer
//
//  Created by Ad<PERSON> on 2024/9/10.
//

#include "mtdocument.h"

#include "worker_thread.h"
#ifndef __APPLE__
#include <flatbuffers/flatbuffers.h>
#include "convert_android.h"
#include "text_measure_android.h"
#endif

#include "final_action.h"
#include "NativeLog.h"
#include "TraceRecorder.h"
#include "trace_scope.h"
#include "types_def.h"
#include "device.h"
#include "text_measure.h"
#include "style_convertor.h"
#include "transform_convertor.h"
#include "layout_utils.h"
#include "render_node.h"
#include "ui_command_buffer.h"
#include "js_engine.h"
#include "performance_monitor.h"

#if DEBUG
#include "debug.h"
#endif

#include "rich_text_component.h"

#include "third_party/blink/renderer/core/dom/document.h"
#include "third_party/blink/renderer/core/html_names.h"
#include "third_party/blink/renderer/core/css/parser/css_parser.h"
#include "third_party/blink/renderer/core/css/parser/css_parser_context.h"
#include "third_party/blink/renderer/core/css/style_engine.h"
#include "third_party/blink/renderer/core/dom/element.h"
#include "third_party/blink/renderer/core/dom/text.h"
#include "third_party/blink/renderer/core/paint/paint_layer.h"
#include "third_party/blink/renderer/core/intersection_observer/intersection_observer.h"
#include "third_party/blink/renderer/core/intersection_observer/intersection_observer_controller.h"
#include "third_party/blink/renderer/platform/wtf/text/wtf_string.h"
#include "third_party/blink/renderer/platform/wtf/text/atomic_string.h"
#include "third_party/blink/renderer/platform/heap/custom_spaces.h"
#include "third_party/blink/renderer/platform/heap/persistent.h"
#include "third_party/blink/renderer/core/css/properties/computed_style_utils.h"
#include "third_party/blink/renderer/platform/heap/thread_state.h"
#include "third_party/blink/renderer/platform/wtf/text/string_statics.h"

#include "TraceUtils.h"
#include "native_dom/bridge/document_registry.h"

#include <vector>
#include <unordered_map>
#include <fstream>
#include <future>

#include "msc_string.h"
#include "native_dom/event_util.h"
#include "native_dom/document.h"

namespace blink {

namespace mt {

namespace {

void GetAbsolutePosition(LayoutBox* box, LayoutUnit* out_x, LayoutUnit* out_y) {
  LayoutUnit x;
  LayoutUnit y;
  for (LayoutBox* parent_box = box ; parent_box; parent_box = parent_box->ContainingBlock()) {
    x += parent_box->Location().X();
    y += parent_box->Location().Y();
  }
  *out_x = x;
  *out_y = y;
}

RenderNode::Rect GetFinalRect(LayoutBox* box) {
  RenderNode::Rect rect;
    if (box->ContainingBlock() != nullptr && box->GetRenderNode()->parent() != nullptr
      && box->ContainingBlock()->GetNode()->getMscTag()
      != box->GetRenderNode()->parent()->getLayoutBox().GetNode()->getMscTag()) {
    LayoutUnit x1, y1;
    LayoutUnit x2, y2;

    GetAbsolutePosition(box, &x1, &y1);
    GetAbsolutePosition(&box->GetRenderNode()->parent()->getLayoutBox(), &x2, &y2);

    rect.x = x1 - x2;
    rect.y = y1 - y2;
    rect.width = box->Size().width;
    rect.height = box->Size().height;
  } else {
    rect.x = box->Location().X();
    rect.y = box->Location().Y();
    rect.width = box->Size().width;
    rect.height = box->Size().height;
  }
  return rect;
}

}; // namespace

class MTDocumentData {
public:
  MTDocumentData() = default;

  bool destroyed_;
  Persistent<blink::Document> document_;
  Persistent<blink::Document> document_for_text_pre_calculate_;
  Config config_;
  class TextMeasurer text_measurer_;
  class UICommandBuffer ui_command_buffer_;
  std::vector<std::shared_ptr<UpdateViewFrameCommamnd>> update_view_frame_commands_; //TODO: jz - 确认是否有用？
  std::function<void(const std::shared_ptr<const std::vector<UICommand>>& buffer)> ui_command_buffer_callback_;
  std::mutex mutex_;

  int perf_id_;
  Size screen_size_;
  Size size_;
  function<void()> delay_layout_;
  Tag root_tag_;
  unordered_map<int, Persistent<Node>> tag2node_;
  unordered_map<int, AtomicString> tag2OriginalStyle_;
  unordered_map<int, Persistent<IntersectionObserver>> intersection_observers_;
#ifndef __APPLE__
  unordered_map<int, shared_ptr<const NativeStyle>> tag2NativeStyle_;
#endif

  std::shared_ptr<JSEngine> js_engine_;
  std::function<void(MTDocument* document, const std::string&)> wxs_load_script_callback_;
  std::function<void(const std::string&)> wxs_transport_callback_;
  bool wxs_ready_;
};

std::shared_ptr<MTDocument> MTDocument::Create(const Config&& config) {
  auto document_ptr = std::shared_ptr<MTDocument>(new MTDocument(std::move(config)), [](MTDocument* document) {
    WorkerThread::thread().addTask([document]() {
      delete document;
    });
  });
  return document_ptr;
}

MTDocument::MTDocument(const Config&& config) : data_(new MTDocumentData) {
  data_->config_ = std::move(config);
  trace_recorder_ptr_ = nullptr;  // 初始化TraceRecorder指针
}

MTDocument::~MTDocument() {
  PerformanceMonitor::destroy(data_->perf_id_);
  delete data_;
  if (enable_native_dom_) {
    document_registry_.reset();
  }
}

void MTDocument::SetPageId(int page_id) {
    if (!enable_native_dom_) {
      return;
    }
    if (page_id == -1) {
        return;
    }
    this->pageId_ = page_id;
    auto that = shared_from_this();
    // 注册到DocumentRegistry中，让DOM侧可以找到
    auto registry = document_registry_.lock();
    registry->registerDocument(page_id, that);
}

void MTDocument::enableNativeDom(bool enable_native_dom) {
  enable_native_dom_ = enable_native_dom;
}

void MTDocument::setup(const std::shared_ptr<msc::native_dom::DocumentRegistry> &documentRegistry, int pageId) {
  {
    std::lock_guard<std::mutex> lock_guard(data_->mutex_);
    data_->destroyed_ = false;
  }

  auto that = shared_from_this();

  if (enable_native_dom_) {
    document_registry_ = documentRegistry;
    this->SetPageId(pageId);
    //注册到DocumentRegistry中，让DOM侧可以找到
    documentRegistry->registerDocument(pageId, that);
  }

  that->data_->perf_id_ = PerformanceMonitor::create();
  that->data_->size_ = Size();
  that->data_->delay_layout_ = nullptr;
  that->data_->root_tag_ = 0;

  WorkerThread::thread().start();
  WorkerThread::thread().addTask([that]() {

    static std::once_flag flag;
    std::call_once(flag, []() {
      ThreadState::AttachCurrentThread();

      WTF::AtomicString::Init();
      WTF::StringStatics::Init();
      Length::Initialize();

      const unsigned kQualifiedNamesCount =
        html_names::kTagsCount + html_names::kAttrsCount/* +
        mathml_names::kTagsCount + mathml_names::kAttrsCount +
        svg_names::kTagsCount + svg_names::kAttrsCount +
        xlink_names::kAttrsCount + xml_names::kAttrsCount +
        xmlns_names::kAttrsCount*/;
      const unsigned kCoreStaticStringsCount =
        kQualifiedNamesCount/* + delivery_type_names::kNamesCount +
        event_interface_names::kNamesCount + event_target_names::kNamesCount +
        event_type_names::kNamesCount + fetch_initiator_type_names::kNamesCount +
        font_family_names::kNamesCount + html_tokenizer_names::kNamesCount +
        http_names::kNamesCount + input_type_names::kNamesCount +
        keywords::kNamesCount + media_feature_names::kNamesCount +
        media_type_names::kNamesCount + performance_entry_names::kNamesCount +
        pointer_type_names::kNamesCount + shadow_element_names::kNamesCount +
        preference_names::kNamesCount + preference_values::kNamesCount*/;
      StringImpl::ReserveStaticStringsCapacityForSize(
        kCoreStaticStringsCount + StringImpl::AllStaticStrings().size());
      QualifiedName::InitAndReserveCapacityForSize(kQualifiedNamesCount);
      html_names::Init();
      CSSParserTokenRange::InitStaticEOFToken();
      font_family_names::Init();
    });

    that->data_->document_ = MakeGarbageCollected<Document>();
    that->data_->document_->SetMTDocument(that.get());
    that->data_->document_->Initialize();

    if (that->config().enable_text_pre_calculate) {
      that->data_->document_for_text_pre_calculate_ = MakeGarbageCollected<Document>();
      that->data_->document_for_text_pre_calculate_->Initialize();
    }

    if (that->config().enable_wxs_engine) {
      that->loadWXSEngine();
    }
  });
}

void MTDocument::DestroyDOMDocument() {
  MSC_RENDERER_LOG_INFO("MTDocument::DestroyDOMDocument for pageId: %d", this->pageId_);
  
  auto task = new std::function(
                                                    [document_registry = document_registry_, page_id = pageId_]() mutable {
                                 MSC_RENDERER_LOG_DEBUG("[native_dom] ServiceThread MTDocument::DestroyDOMDocument for pageId: %d", page_id);
                                 //document_registry_
                                 auto document_registry_strong = document_registry.lock();
                                 if  (document_registry_strong) {
                                   auto domDocument = document_registry_strong->getDOMDocument(page_id);
                                   if (domDocument) {
                                     //触发DOM document的关闭流程，调完此方法后JS线程的NativeDOM就不会往blink发消息了
                                     domDocument->Close();
                                     //注销page_id和DOM document的绑定关系，后续blink线程（主要是事件）就找不到DOM document了，
                                     //但此时页面已关闭，且已经经历了从主线程到blink线程再到JS线程的切换，前序事件应该都已处理完成了，
                                     //后续如果还有事件也应该忽略
                                     document_registry_strong->unregisterDOMDocument(page_id);
                                   } else {
                                     MSC_RENDERER_LOG_ERROR("MTDocument::DestroyDOMDocument cannot find DOM document for pageId: %d", page_id);
                                   }
                                   //注销page_id和MTDocument的对应关系，后续如果有JS侧的API调用到Native侧，就找不到MTDocument了
                                   //但上面的domDocument->Close()保证了MessasgeProxy会阻断NativeDOM往blink发消息的流程，也就不会找MTDocument了
                                   document_registry_strong->unregisterDocument(page_id);
                                 } else {
                                   MSC_RENDERER_LOG_ERROR("MTDocument::DestroyDOMDocument cannot find document registry");
                                 }
                                                    });
  MSC_RENDERER_LOG_INFO("MSCMPUIManager create destroy task: %p", task);
     bool success = this->PostTaskToLogicTaskRunner(task);
  if (!success) {
    (*task)();
    delete task;
  }
}

//UIManager模块invalidate时触发
void MTDocument::destroy() {
  data_->destroyed_ = true;

  auto that = shared_from_this();
  WorkerThread::thread().addTask([that]() {
    if (that->enable_native_dom_) {
      that->data_->intersection_observers_.clear();
      //这里会给JS线程抛任务，清理DOM document等结点
      that->DestroyDOMDocument();
      
      //在blink线程清除runner，从而销毁runner block捕获的MSCService
      that->SetLogicTaskRunner(nullptr);
    }
  });
}

Tag MTDocument::perfId() const {
  return data_->perf_id_;
}

void MTDocument::setUICommandBufferCallback(UICommandBufferCallback callback) {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, callback]() {
    if (that->data_->destroyed_) {
      return;
    }
    that->data_->ui_command_buffer_.setCallback(callback);
  });
}

Size& MTDocument::size() const {
  std::lock_guard<std::mutex> lock_guard(data_->mutex_);
  return data_->size_;
}

void MTDocument::setSize(Size&& size) {
  function<void()> delay_layout;
  {
    std::lock_guard<std::mutex> lock_guard(data_->mutex_);
    if (data_->destroyed_ || size == data_->size_) {
      return;
    }
    data_->size_ = std::move(size);
    delay_layout = data_->delay_layout_;
    data_->delay_layout_ = nullptr;
  }

  auto that = shared_from_this();
  WorkerThread::thread().addTask([that]() {
    if (that->data_->destroyed_) {
      return;
    }
#ifndef __APPLE__
    mt::Device::initScreenData(that->data_->size_.width, that->data_->size_.height, that->data_->size_.density);
#endif
    that->data_->document_->GetLayoutView()->setSize(that->data_->size_.width, that->data_->size_.height);
    that->data_->document_->GetStyleEngine().UpdateViewportSize();
  });

  if (delay_layout) {
    delay_layout();
  }
}

void MTDocument::addCSS(const std::string&& css) {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, css_moved = std::move(css)]() {
    if (that->data_->destroyed_) {
      return;
    }

    that->addCSSInternal(css_moved);
  });
}

void MTDocument::addCSSFile(const std::string&& filename) {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, filename_moved = std::move(filename)]() {
    if (that->data_->destroyed_) {
      return;
    }

    ifstream file(filename_moved);
    if (!file.is_open()) {
      return;
    }

    ostringstream oss;
    oss << file.rdbuf();
    string css = oss.str();

    that->addCSSInternal(css);

    file.close();
  });
}

void MTDocument::addCSSInternal(const string &css) {
  auto token = PerformanceMonitor::createToken();
  PerformanceMonitor::beginEvent(data_->perf_id_, token, "css", 0);
  {
    TRACE_SCOPE(this, "css", trace::TraceLevel::P1);
    auto sheet = CSSStyleSheet::CreateInline();
    WTF::String str = WTF::String::FromUTF8(css);
    CSSParserContext ctx(blink::kHTMLStandardMode, blink::SecureContextMode::kSecureContext);
    blink::CSSParser::ParseSheet(&ctx, sheet->Contents(), str);

    auto add_sheet_to_engine = [](TreeScope& scope, StyleEngine& engine, CSSStyleSheet* sheet) {
      blink::StyleEngine::RuleSetScope rule_set_scope;
      auto ruleset = rule_set_scope.RuleSetForSheet(engine, sheet);
      blink::ActiveStyleSheetVector sheets;
      sheets.push_back(make_pair(sheet, ruleset));
      engine.ApplyRuleSetChanges(scope, sheets);
    };

    auto document = data_->document_;
    add_sheet_to_engine(*document, document->GetStyleEngine(), sheet);

    if (config().enable_text_pre_calculate) {
      auto document = data_->document_for_text_pre_calculate_;
      add_sheet_to_engine(*document, document->GetStyleEngine(), sheet);
    }
  }
  PerformanceMonitor::endEvent(data_->perf_id_, token, "css", 0);
}

void MTDocument::createNode(Tag tag,
                            const std::string&& view_name,
                            Tag root_tag,
                            const std::shared_ptr<const Props>& props) {
  MSC_TRACE_SCOPED_EVENT(MTDocument::createNode);

  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, tag, moved_view_name = std::move(view_name), root_tag,
                                  moved_props = std::move(props)]() {
    if (that->data_->destroyed_) {
      return;
    }

    that->data_->ui_command_buffer_.SetInBatch(true);

    auto dom_token = PerformanceMonitor::createToken();
    PerformanceMonitor::beginEvent(that->data_->perf_id_, dom_token, "dom", 0);
    {
      TRACE_SCOPE(that.get(), "dom", trace::TraceLevel::P1);
      auto create_node_token = PerformanceMonitor::createToken();
      PerformanceMonitor::beginEvent(that->data_->perf_id_, create_node_token, "dom.createNode", 0);
      {
          TRACE_SCOPE(that.get(), "dom_createNode", trace::TraceLevel::P1);
          Node *node = nullptr;
          Element *element = nullptr;

          if (moved_view_name == "MSCRawText") {
            auto text = moved_props->getProp("text");
            if (!text.isNull()) {
              node = blink::Text::Create(*that->data_->document_, WTF::String::FromUTF8(text.stringValue()));
          } else {
              node = blink::Text::Create(*that->data_->document_, WTF::String::FromUTF8(""));
            }
          } else {
            auto component_name = moved_props->getProp("tagName");
            auto name = "mt-view";
            if (!component_name.isNull()) {
              name = component_name.stringValue().c_str();
            }
            auto html_tag_name = moved_props->getProp("htmlTagName");
            auto tag_name = name;
            if (!html_tag_name.isNull()) {
              tag_name = html_tag_name.stringValue().c_str();
          }
            node = ComponentRegistry::createElement(*that->data_->document_,
                                                    tag_name,
                                                    name);
            element = To<blink::Element>(node);
            element->setMtDocument(that);
            element->setPlatformViewName(WTF::AtomicString::FromUTF8(moved_view_name.c_str()));
          }
          node->setMscTag(tag);
          node->setRootTag(root_tag);
          that->addNodeInternal(tag, node);

          if (element) {
            moved_props->forEach([&element](const std::string& key, const PropValue& value) {
              auto name = AtomicString::FromUTF8(key.c_str());
              if (name == "class" || name == "id") {
                element->setAttribute(name, String::FromUTF8(value.stringValue()));
              } else if (name == "style") {
                element->setAttribute(name, String::FromUTF8(value.stringValue()));
                element->setAttribute(AtomicString::FromUTF8("originStyle"), String::FromUTF8(value.stringValue()));
              }
            });
            element->setProps(moved_props);

            that->updateRelatedAttributesForProps(moved_props, element, tag);

            if (tag == root_tag) {
              that->data_->root_tag_ = root_tag;
              AtomicString name = AtomicString::FromUTF8("style");
              String value = String::FromUTF8("width:100%;height:100%");
              element->setAttribute(name, value);
              that->data_->document_->ParserAppendChild(node);
            }
          }
        }
        PerformanceMonitor::endEvent(that->data_->perf_id_, create_node_token, "dom.createNode", 0);
    }
    PerformanceMonitor::endEvent(that->data_->perf_id_, dom_token, "dom", 0);
  });
}

void MTDocument::updateRelatedAttributesForProps(const std::shared_ptr<const Props>& props,
                                                 blink::Element *element, Tag tag) {
  auto refresher_enabled_prop = props->getProp("refresherEnabled");
  auto slot_prop = props->getProp("slotName");
  bool has_refresher_enabled_prop = !refresher_enabled_prop.isNull();
  bool has_slot_name_prop = !slot_prop.isNull();

  if (has_refresher_enabled_prop || has_slot_name_prop) {
    auto current_style = props->getProp("style");
    bool has_current_style = !current_style.isNull();

    auto saved_original_style = data_->tag2OriginalStyle_.find(tag);
    bool has_saved_original_style = saved_original_style != data_->tag2OriginalStyle_.end();
    
    StringBuilder style_builder;
    auto style = AtomicString("");
    bool add_prop = (has_refresher_enabled_prop && refresher_enabled_prop.boolValue()) ||
                      (has_slot_name_prop && slot_prop.stringValue() == "refresher");

    if (add_prop) {
      if (has_refresher_enabled_prop) {
        style_builder.Append("position: relative; ");
      }
      if (has_current_style) {
        //取当前属性
        style = AtomicString::FromUTF8(current_style.stringValue().c_str());
        data_->tag2OriginalStyle_[tag] = style;
      } else {
        if (has_saved_original_style) {
          //取存储的original属性，因为这时getAttribute()得到的结果是拼了position: relative的结果
          style = saved_original_style->second;
        } else {
          //取存储的属性
          style = element->getAttribute(AtomicString::FromUTF8("style"));
        }
      }
      style_builder.Append(style);
      if (has_slot_name_prop) {
        style_builder.Append("; position: absolute; bottom: 100%");
      }
    } else {
      if (has_current_style || has_saved_original_style) {
        //如果当前有最新style，用最新值；否则取存储的original属性，因为这时getAttribute()得到的结果是拼了position: relative的结果
        style = has_current_style ? AtomicString::FromUTF8(current_style.stringValue().c_str()) : saved_original_style->second;;
        //delete saved orig style if needed
        if (has_saved_original_style) {
          data_->tag2OriginalStyle_.erase(saved_original_style);
        }
      } else {
        //取存储的属性
        style = element->getAttribute(AtomicString::FromUTF8("style"));
      }
      style_builder.Append(style);
    }
    element->setAttribute(AtomicString::FromUTF8("style"), style_builder.ToString());
  }
}

void MTDocument::updateNode(Tag tag,
                            const std::string&& view_name,
                            const std::shared_ptr<const Props>& props) {
  MSC_TRACE_SCOPED_EVENT(MTDocument::updateNode);
  auto that = shared_from_this();

  WorkerThread::thread().addTask([tag, that, moved_view_name = std::move(view_name), moved_props = std::move(props)]() {
    if (that->data_->destroyed_) {
      return;
    }

    that->data_->ui_command_buffer_.SetInBatch(true);

    auto dom_token = PerformanceMonitor::createToken();
    PerformanceMonitor::beginEvent(that->data_->perf_id_, dom_token, "dom", 0);
    {
        TRACE_SCOPE(that.get(), "dom", trace::TraceLevel::P1);
        auto update_node_token = PerformanceMonitor::createToken();
        PerformanceMonitor::beginEvent(that->data_->perf_id_, update_node_token, "dom.updateNode",
                                       0);
        {
            TRACE_SCOPE(that.get(), "dom_updateNode", trace::TraceLevel::P1);
            do {
                auto node = that->getNode(tag);
                if (!node) {
                    break;
                }

                if (IsA<blink::Text>(node)) {
                    auto parent_node = node->parentNode();
                    if (!IsA<blink::Element>(parent_node)) {
                        break;
                    }

                    auto value = moved_props->getProp("text");
                    auto new_node = blink::Text::Create(*that->data_->document_,
                                                        WTF::String::FromUTF8(value.stringValue()));
                    new_node->setMscTag(node->getMscTag());
                    that->data_->tag2node_[node->getMscTag()] = new_node;
                    parent_node->ParserInsertBefore(new_node, *node);
                    parent_node->ParserRemoveChild(*node);
                } else {
                    auto element = To<blink::Element>(node);
                    moved_props->forEach(
                            [&element](const std::string &key, const PropValue &value) {
                                auto name = AtomicString::FromUTF8(key.c_str());
                                if (name == "class" || name == "id") {
                                    element->setAttribute(name,
                                                          String::FromUTF8(value.stringValue()));
                                } else if (name == "style") {
                                    element->setAttribute(name,
                                                          String::FromUTF8(value.stringValue()));
                                    element->setAttribute(AtomicString::FromUTF8("originStyle"),
                                                          String::FromUTF8(value.stringValue()));
                                } else if (name == "wxsStyle") {
                                    const AtomicString &origin_style = element->getAttribute(
                                            AtomicString::FromUTF8("originStyle"));
                                    const String &wxs_style = String::FromUTF8(value.stringValue());
                                    element->setAttribute(AtomicString::FromUTF8("style"),
                                                          origin_style + ";" + wxs_style);
                                }
                            });
                    element->setProps(PropsBuilder(element->getProps(), moved_props).getProps());

                    LayoutBox *layout_box = element->GetLayoutBox();
                    if (layout_box && layout_box->GetRenderNode()) {
                        layout_box->GetRenderNode()->setPropsChanged(true);
                    }
                }
            } while (false);
        }
        PerformanceMonitor::endEvent(that->data_->perf_id_, update_node_token, "dom.updateNode", 0);
    }
    PerformanceMonitor::endEvent(that->data_->perf_id_, dom_token, "dom", 0);

  });
}

void MTDocument::setWidthHeightFix(Tag tag, bool width_fix, bool height_fix, float width, float height) {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, tag, width_fix, height_fix, width, height]() {
    if (that->data_->destroyed_) {
      return;
    }

    Element* node = DynamicTo<Element>(that->getNode(tag));
    if (!node) {
      return;
    }

    auto element = To<blink::Element>(node);
    element->setWidthHeightFix(width_fix, height_fix, width, height);
    element->SetNeedsStyleRecalc(StyleChangeType::kInlineIndependentStyleChange);
    that->layoutRoot(LayoutReason::BatchDidComplete);
  });
}

void MTDocument::setChildren(Tag parent_tag, const std::shared_ptr<const vector<Tag>>& child_tags) {
  auto changes = make_shared<blink::mt::MTDocument::ChildrenChanges>();
  for (auto tag : *child_tags) {
    changes->add_child_msc_tags.push_back(tag);
  }
  manageChildren(parent_tag, changes);
}

void MTDocument::removeNode(Tag tag) {
  MSC_TRACE_SCOPED_EVENT(MTDocument::removeNode);
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, tag]() {
    if (that->data_->destroyed_) {
      return;
    }

    auto dom_token = PerformanceMonitor::createToken();
    PerformanceMonitor::beginEvent(that->data_->perf_id_, dom_token, "dom", 0);
      {
          TRACE_SCOPE(that.get(), "dom", trace::TraceLevel::P1);

          auto update_children_token = PerformanceMonitor::createToken();
          PerformanceMonitor::beginEvent(that->data_->perf_id_, update_children_token,
                                         "dom.removeNode", 0);
          {
              TRACE_SCOPE(that.get(), "dom_removeNode", trace::TraceLevel::P1);

              auto node = that->getNode(tag);
              if (!node || !node->parentNode()) {
                  return;
              }
              auto parent_tag = node->parentNode()->getMscTag();
              auto parent_node = that->getNode(parent_tag);
              if (!parent_node || !IsA<Element>(parent_node)) {
                  return;
              }
              auto parent_element = To<Element>(parent_node);

              auto remove_msc_tags = make_shared<vector<int>>();
              that->removeNodeInternal(tag, *remove_msc_tags);
              parent_element->ParserRemoveChild(*node);

              auto &ui_command_buffer = that->data_->ui_command_buffer_;
              std::vector<DeleteViewsCommamnd::DeleteOp> delete_ops;
              for (Tag tag: *remove_msc_tags) {
                  delete_ops.push_back(tag);
              }
              ui_command_buffer.ScheduleCommand(
                      ui_command_buffer.deleteViewsCommand(std::move(delete_ops)));
          }
          PerformanceMonitor::endEvent(that->data_->perf_id_, update_children_token,
                                       "dom.removeNode", 0);
      }
    PerformanceMonitor::endEvent(that->data_->perf_id_, dom_token, "dom", 0);
  });
}

void MTDocument::manageChildren(Tag parent_tag, const std::shared_ptr<const ChildrenChanges>& changes) {
  MSC_TRACE_SCOPED_EVENT(MTDocument::manageChildren)
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, parent_tag, changes]() {
    if (that->data_->destroyed_) {
      return;
    }

    that->data_->ui_command_buffer_.SetInBatch(true);

    auto dom_token = PerformanceMonitor::createToken();
    PerformanceMonitor::beginEvent(that->data_->perf_id_, dom_token, "dom", 0);
    {
        TRACE_SCOPE(that.get(), "dom", trace::TraceLevel::P1);

        auto update_children_token = PerformanceMonitor::createToken();
        PerformanceMonitor::beginEvent(that->data_->perf_id_, update_children_token,
                                       "dom.updateChildren", 0);
        {
            TRACE_SCOPE(that.get(), "dom_updateChildren", trace::TraceLevel::P1);

            auto remove_msc_tags = make_shared < vector < int >> ();
            do {
                auto parent_node = that->getNode(parent_tag);
                if (!parent_node || !IsA<Element>(parent_node)) {
                    break;
                }

                auto *parent_element = To<Element>(parent_node);
                int children_count = 0;
                for (auto *child = parent_element->firstChild(); child; child = child->nextSibling()) {
                    ++children_count;
                }
                bool failed = false;

                // 1. 收集需要移动、删除的元素
                vector < Node * > nodes_to_remove;
                if (changes->remove_at_indices.size() > 0) {
                    for (auto child_index: changes->remove_at_indices) {
                        auto child_node = that->getChildNode(parent_element, child_index);
                        if (!child_node) {
                            failed = true;
                            break;
                        }
                        nodes_to_remove.push_back(child_node);
                    }
                    if (failed) {
                        break;
                    }
                }

                vector < Node * > nodes_to_move;
                if (changes->move_from_indices.size() > 0) {
                    for (auto child_index: changes->move_from_indices) {
                        auto child_node = that->getChildNode(parent_element, child_index);
                        if (!child_node) {
                            failed = true;
                            break;
                        }
                        nodes_to_move.push_back(child_node);
                    }
                    if (failed) {
                        break;
                    }
                }

                // 2. 临时删除移动的元素，删除元素
                for (auto child_node: nodes_to_move) {
                    --children_count;
                    parent_element->ParserRemoveChild(*child_node);
                }
                for (auto child_node: nodes_to_remove) {
                    --children_count;
                    that->removeNodeInternal(child_node->getMscTag(), *remove_msc_tags);
                    parent_element->ParserRemoveChild(*child_node);
                }

                // 3. 收集移动和插入元素的位置
                auto move_to_size = changes->move_to_indices.size();
                auto add_size = changes->add_child_msc_tags.size();
                if (move_to_size > 0 || add_size > 0) {
                    map<int, Node *> index2nodes;
                    vector<int> indexes;

                    if (move_to_size > 0) {
                        for (auto i = 0; i < move_to_size; i++) {
                            ++children_count;
                            auto index = changes->move_to_indices[i];
                            auto child_node = nodes_to_move[i];
                            index2nodes.insert(make_pair(index, child_node));
                            indexes.push_back(index);
                        }
                    }

                    if (add_size > 0) {
                        for (auto i = 0; i < add_size; i++) {
                            ++children_count;
                            auto index = 0;
                            if (i < changes->add_at_indices.size()) {
                                index = changes->add_at_indices[i];
                            } else {
                                index = children_count - 1;
                            }
                            auto child_tag = changes->add_child_msc_tags[i];
                            auto iter = that->data_->tag2node_.find(child_tag);
                            if (iter == that->data_->tag2node_.end()) {
                                failed = true;
                                break;
                            }
                            auto child_node = iter->second;
                            index2nodes.insert(make_pair(index, child_node));
                            indexes.push_back(index);
                        }
                    }

                    if (failed) {
                        break;
                    }

                    sort(indexes.begin(), indexes.end());

                    // 4. 移动和插入元素
                    auto parent_tag_name = parent_element->componentName().Utf8();
                    bool parent_is_text_box = parent_tag_name == "mt-text";
                    bool parent_is_scroll_view = parent_tag_name == "mt-scroll-view"
                                                 ||
                                                 parent_tag_name == "mt-msc-lazy-load-scroll-view"
                                                 || parent_tag_name == "mt-swiper";

                    bool parent_is_swiper = parent_tag_name == "mt-swiper";

                    bool enable_block_layout = that->config().enable_block_layout;
                    for (auto child_index: indexes) {
                        auto child_node = index2nodes[child_index];
                        if (IsA<Element>(child_node)) {
                            auto *child_element = To<Element>(child_node);
                            auto child_tag_name = child_element->componentName();
                            bool is_text =
                                    child_tag_name == "mt-text" || child_tag_name == "-mt-break";
                            bool is_inline_text = parent_is_text_box && is_text;

                            if (is_inline_text) {
                                child_element->setIsText(false);
                                child_element->setForceStackingContext(false);
                                child_element->setForceDisplay(ForceDisplay::kInline);
                                child_element->setNoShrink(false);
                            } else {
                                bool is_inline_box = parent_is_text_box && !is_text;
                                bool is_scroll_view = child_tag_name == "mt-scroll-view"
                                                      || child_tag_name ==
                                                         "mt-msc-lazy-load-scroll-view"
                                                      || child_tag_name == "mt-swiper";
                                bool is_movable_view = child_tag_name == "mt-movable-view";

                                child_element->setIsText(is_text);
                                child_element->setForceStackingContext(
                                        is_scroll_view || parent_is_scroll_view || is_movable_view);
                                if (is_inline_box) {
                                    child_element->setForceDisplay(
                                            enable_block_layout ? ForceDisplay::kInlineBox
                                                                : ForceDisplay::kInlineFlex);
                                } else {
                                    if (is_text) {
                                        child_element->setForceDisplay(ForceDisplay::kBlock);
                                    } else {
                                        child_element->setForceDisplay(
                                                enable_block_layout ? ForceDisplay::kBox
                                                                    : ForceDisplay::kFlex);
                                    }
                                }
                                child_element->setNoShrink(parent_is_scroll_view);

                                if (is_text) {
                                    that->preCalculateText(child_element);
                                }
                            }
                        } else if (!parent_is_text_box) {
                            // 文本节点，父节点必须是 text
                            break;
                        }

                        auto next_child_node = that->getChildNode(parent_element, child_index);
                        if (next_child_node) {
                            parent_element->ParserInsertBefore(child_node, *next_child_node);
                        } else {
                            parent_element->ParserAppendChild(child_node);
                        }
                    }
                }
            } while (false);

            auto &ui_command_buffer = that->data_->ui_command_buffer_;
            std::vector<DeleteViewsCommamnd::DeleteOp> delete_ops;
            for (Tag tag: *remove_msc_tags) {
                delete_ops.push_back(tag);
            }
            ui_command_buffer.ScheduleCommand(ui_command_buffer.deleteViewsCommand(std::move(delete_ops)));
        }
        PerformanceMonitor::endEvent(that->data_->perf_id_, update_children_token, "dom.updateChildren", 0);
    }
    PerformanceMonitor::endEvent(that->data_->perf_id_, dom_token, "dom", 0);
  });
}

void TraversePaintLayerTree(PaintLayer *layer,
                        std::function<void(PaintLayer *layer, bool* skip_children)> callback,
                        int depth = 0)
{
  if (!layer) {
    return;
  }
#if ENABLE_STACKING_CHILD_PRINT
  if (depth == 0) {
    printf("\n[blink] Paint Layer Tree:=============================\n");
  }
  printf("[blink] ");
  for (int i = 0; i < depth; i++) {
    printf("  ");
  }
  auto layout_box = layer->GetLayoutBox();
  if (layer->msc_stacking_context_dirty_) {
    printf("*");
  }
  printf("%d, %s, %d, z:%d, %s\n", layout_box->GetNode()->getMscTag(), PostionToString(layout_box->Style()->GetPosition()).Utf8().c_str(), layout_box->IsStackingContext(), layout_box->Style()->ZIndex(), layout_box->ToString().Utf8().c_str());
#endif
  bool skip_children = false;
  callback(layer, &skip_children);
  
  if (!skip_children) {
    PaintLayer *child = layer->FirstChild();
    while (child) {
      TraversePaintLayerTree(child, callback, depth + 1);
      child = child->NextSibling();
    }
  }
#if ENABLE_STACKING_CHILD_PRINT
  if (depth == 0) {
    printf("\n[blink] Paint Layer Tree:----------------------------\n");
  }
#endif
}

void MTDocument::QueryEnhanced(QueryEnhancedParams&& params,
                               msc::JSCallbackInfo callback_info) {
  if (!enable_native_dom_) {
    return;
  }
  WorkerThread::thread().addTask([that = shared_from_this(),
                                  params = std::move(params),
                                  callback_info]() mutable {
    if (that->data_->destroyed_) {
      return;
    }

    auto registry = that->document_registry_.lock();

    // get location, size and scroll offset
    if (params.is_viewport) {
      params.tags.clear();
      params.tags.push_back(that->data_->root_tag_);
    }
    unsigned size = params.tags.size();
    QueryEnhancedEntries entries(size);
    for (unsigned i = 0; i < size; ++i) {
      // NodeNotFound
      Element* element{};
      if (auto itr = that->data_->tag2node_.find(params.tags[i]);
          itr != that->data_->tag2node_.end()) {
        if (auto node = itr->second; node) {
          element = To<Element>(node.Get());
        }
      }
      if (!element) {
        entries[i].invalid = true;
        registry->ReportMetrics(msc::NativeDOMMetrics::ErrCode::MTDocumentNodeNotFound,
                                msc::NativeDOMMetrics::API::kMTDocumentQueryEnhanced);
        continue;
      }

      auto layout_object = element->GetLayoutObject();

      // NoLayoutBox
      LayoutBox* layout_box{};
      if (layout_object) {
        layout_box = To<LayoutBox>(layout_object);
      }
      if (!layout_box) {
        entries[i].invalid = true;
        registry->ReportMetrics(msc::NativeDOMMetrics::ErrCode::MTDocumentNoLayoutBox,
                                msc::NativeDOMMetrics::API::kMTDocumentQueryEnhanced);
        continue;
      }

      if (params.need_location) {
        auto rect_relative_to_root = layout_object->AbsoluteBoundingBoxRectF();
        entries[i].x = Device::gridAlignedValue(rect_relative_to_root.origin().x());
        entries[i].y = Device::gridAlignedValue(rect_relative_to_root.origin().y());
      }
      if (params.need_size) {
        auto size = layout_box->Size();
        entries[i].width = Device::gridAlignedValue(size.width.ToFloat());
        entries[i].height = Device::gridAlignedValue(size.height.ToFloat());
      }
      if (params.need_scroll_offset) {
        auto scroll_offset = layout_box->MscScrolledContentOffset();
        entries[i].scroll_top = Device::gridAlignedValue(scroll_offset.top.ToFloat());
        entries[i].scroll_left = Device::gridAlignedValue(scroll_offset.left.ToFloat());
      }
    }

    that->PostCallbackToService(
        callback_info, [entries = std::move(entries)](msc::JSCallable* js_callback, msc::native_dom::Element* element) {
          if (js_callback->api() != msc::JSCallable::API::QueryEnhanced) return;
          auto callback = static_cast<msc::JSCallbackForQueryEnhanced*>(js_callback);
          if (!callback) return;
          callback->entries_ = std::move(entries);
          callback->element_ = element;
        });
  });
}

void MTDocument::CreateIntersectionObserver(
    CreateIntersectionObserverParams&& params,
    msc::JSCallbackInfo callback_info) {
  WorkerThread::thread().addTask([weak_that = weak_from_this(),
                                  that_params = std::move(params),
                                  callback_info]() {
    auto that = weak_that.lock();
    if (!that) {
      return;
    }
    if (that->data_->destroyed_) {
      return;
    }

    // params
    blink::IntersectionObserver::Params params;
    // params.root
    Tag root_tag = that_params.is_viewport_ ? that->data_->root_tag_
                                            : that_params.root_tag_;
    // params.margin_target =
    //     IntersectionObserver::MarginTarget::kApplyMarginToRoot;
    auto itr = that->data_->tag2node_.find(root_tag);
    if (itr != that->data_->tag2node_.end()) {
      params.root = itr->second.Get();
    } else {
      return;
    }

    // params.root_margin
    if (that_params.margins_str_.size()) {
      params.ParseRootMargins(WTF::String::FromUTF8(that_params.margins_str_));
    } else if (auto sz = that_params.margins_.size(); sz) {
      params.margin.reserve(sz);
      for (auto& margin : that_params.margins_) {
        params.margin.push_back(Length::Fixed(margin));
      }
    }
    // params.thresholds
    params.thresholds.assign(that_params.thresholds_);
    params.SortThresholds();

    // callback
    auto callback =
        [weak_that = weak_that, callback_info](
            const HeapVector<Member<IntersectionObserverEntry>>& entries) {
          auto that = weak_that.lock();
          if (!that) {
            return;
          }
          std::vector<IntersectionEntryData> entry_data;
          for (auto& entry : entries) {
            auto intersection_ratio = entry->intersectionRatio();
            auto bounding_client_rect = entry->boundingClientRect();
            auto root_bounds = entry->rootBounds();
            auto intersection_rect = entry->intersectionRect();
            auto is_intersecting = entry->isIntersecting();
            auto is_visible = entry->isVisible();
            auto target = entry->target();

            entry_data.emplace_back(IntersectionEntryData{
                .intersection_ratio = intersection_ratio,
                .bounding_client_rect =
                    IntersectionEntryData::Rect{
                        .x = bounding_client_rect->x(),
                        .y = bounding_client_rect->y(),
                        .width = bounding_client_rect->width(),
                        .height = bounding_client_rect->height()},
                .root_bounds_rect =
                    IntersectionEntryData::Rect{
                        .x = root_bounds->x(),
                        .y = root_bounds->y(),
                        .width = root_bounds->width(),
                        .height = root_bounds->height()},
                .intersection_rect =
                    IntersectionEntryData::Rect{
                        .x = intersection_rect->x(),
                        .y = intersection_rect->y(),
                        .width = intersection_rect->width(),
                        .height = intersection_rect->height()},
                .is_intersecting = is_intersecting,
                .is_visible = is_visible,
                .target_tag = target ? target->getMscTag() : 0});
          }

          that->PostCallbackToService(
              callback_info,
              [entries = std::move(entry_data)](msc::JSCallable* js_callback, msc::native_dom::Element *) {
                if (js_callback->api() !=
                    msc::JSCallable::API::IntersectionObserver)
                  return;
                auto callback =
                    static_cast<msc::JSCallbackForIntersectionObserver*>(
                        js_callback);
                if (!callback) return;
                callback->entries_ = std::move(entries);
              });
        };

    params.behavior = IntersectionObserver::kPostTaskToDeliver;
    auto intersection_observer_id = that_params.intersection_observer_id_;

    // create observer
    auto observer = blink::IntersectionObserver::Create(
        *(that->data_->document_.Get()), std::move(callback),
        std::move(params));
    that->data_->intersection_observers_[intersection_observer_id] = observer;
  });
}

void MTDocument::IntersectionObserverObserve(int intersection_observer_id,
                                             int tag) {
  if (!enable_native_dom_) {
    return;
  }
  WorkerThread::thread().addTask([weak_that = weak_from_this(),
                                  intersection_observer_id, tag]() {
    auto that = weak_that.lock();
    if (that->data_->destroyed_) {
      return;
    }
    
    auto registry = that->document_registry_.lock();
    
    // ObserverNotFound
    IntersectionObserver* observer{};
    if (auto itr =
        that->data_->intersection_observers_.find(intersection_observer_id);
        itr != that->data_->intersection_observers_.end()) {
      observer = itr->second;
    }
    if (!observer) {
      registry->ReportMetrics(msc::NativeDOMMetrics::ErrCode::MTDocumentObserverNotFound,
                              msc::NativeDOMMetrics::API::kMTDocumentIntersectionObserverObserve);
      return;
    }
    
    // disconnect
    if (tag == 0) {
      observer->disconnect();
      return;
    }
    
    // NodeNotFound
    Element* element{};
    if (auto itr = that->data_->tag2node_.find(tag);
        itr != that->data_->tag2node_.end()) {
      if (auto node = itr->second; node) {
        element = To<Element>(node.Get());
      }
    }
    if (!element) {
      registry->ReportMetrics(msc::NativeDOMMetrics::ErrCode::MTDocumentNodeNotFound,
                              msc::NativeDOMMetrics::API::kMTDocumentIntersectionObserverObserve);
      return;
    }
    
    // observe
    observer->observe(element);
  });
}

Element* MTDocument::getElementSync(Tag tag) {
  return DynamicTo<Element>(getNode(tag));
}

namespace {

std::string Trim(const std::string &str) {
  size_t start = str.find_first_not_of(" ");
  if (start == std::string::npos) return "";
  size_t end = str.find_last_not_of(" ");
  return str.substr(start, end - start + 1);
}

std::vector<std::string> SplitAndTrim(
    const std::string &input,
    const std::string &sep)
{
    std::vector<std::string> tokens;
    if (sep.empty()) {
        tokens.push_back(Trim(input));
        return tokens;
    }

    size_t start = 0;
    size_t end = input.find(sep);

    while (end != std::string::npos) {
        std::string token = input.substr(start, end - start);
        token = Trim(token);
        if (!token.empty()) {
            tokens.push_back(token);
        }
        start = end + sep.length();
        end = input.find(sep, start);
    }

    // 添加最后一个子字符串
    std::string lastToken = input.substr(start);
    lastToken = Trim(lastToken);
    if (!lastToken.empty()) {
        tokens.push_back(lastToken);
    }

    return tokens;
}

const std::string FilterSelector(Element* element, const std::string& selector, bool* success) {
  std::string filtered_selector;

  if (element) {
    *success = true;

    const std::vector<std::string> selector_parts = SplitAndTrim(selector, " ");
    for (const std::string& sub_selector : selector_parts) {
      bool is_ID_selector = sub_selector[0] == '#';
      bool is_class_selector = sub_selector[0] == '.';
      if (is_ID_selector) {
        filtered_selector += sub_selector;
      } else if (is_class_selector) {
        std::string class_prefix;
        if (element->getMscTag() == 1) {
          class_prefix = "p-";
        } else {
          // 自定义组件节点未包含 classPrefix，需要从它子节点里找
          const std::string& is = element->getProp("is").stringValue();
          if (is.length() > 0 && is[0] == '/') {
            element = DynamicTo<Element>(element->firstChild());
          }
          if (element) {
            class_prefix = element->getProp("classPrefix").stringValue();
          }
        }

        filtered_selector += "." + class_prefix + sub_selector.substr(1) + " ";
      } else {
        *success = false;
        break;
      }
    }
  } else {
    *success = false;
  }

  return success ? filtered_selector : "";
}

Element* QueryElementSecondPart(Element* element, const std::string& selector) {
  Element* query_element = nullptr;

  for (Element* child = DynamicTo<Element>(element->firstChild()); child;
       child = DynamicTo<Element>(child->nextSibling())) {
    const std::string& is = child->getProp("is").stringValue();
    if (is.length() == 0) {
      break;
    }

    if (is[0] == '/') {
      bool success;
      const std::string filtered_selector = FilterSelector(child, selector, &success);
      if (!success) {
        break;
      }
      query_element = element->querySelector(AtomicString::FromUTF8(filtered_selector.c_str()));
    } else {
      query_element = QueryElementSecondPart(child, selector);
    }

    if (query_element) {
      break;
    }
  }

  return query_element;
}

}; // namespace

Element* MTDocument::queryElementSync(Tag tag, const std::string& selectors) {
  Element* element = getElementSync(tag);
  if (!element) {
    return nullptr;
  }

  // 并列选择器用逗号分隔，如 .a, .b
  const std::vector<std::string> selector_list = SplitAndTrim(selectors, ",");
  for (auto& selector : selector_list) {
    const std::vector<std::string> pair = SplitAndTrim(selector, ">>>");

    if (pair.size() == 0) {
      return nullptr;
    }

    // 如果非跨子组件选择器，尝试查找选择器对应的元素
    // 如果是跨子组件选择器，尝试查找选择器的第一部分对应的元素
    bool success;
    const std::string filtered_first_selector = FilterSelector(element, pair[0], &success);
    if (!success) {
      return nullptr;
    }
    Element* query_element = element->querySelector(AtomicString::FromUTF8(filtered_first_selector.c_str()));
    if (query_element && pair.size() == 1) {
      return query_element;
    } else if (pair.size() == 1) {
      continue;
    }

    // 基于前面的结果，尝试查找选择器第二部分对应的元素
    const std::string& second_selector = pair[1];
    query_element = QueryElementSecondPart(query_element, second_selector);
    if (query_element) {
      return query_element;
    }
  }

  return nullptr;
}

const Rect MTDocument::getClientRectSync(Tag tag) {
  auto* element = DynamicTo<Element>(getNode(tag));
  if (!element) {
    return Rect();
  }
  auto* box = element->GetLayoutBox();
  if (!box) {
    return Rect();
  }

  LayoutUnit x;
  LayoutUnit y;
  LayoutUnit width = box->Size().width;
  LayoutUnit height = box->Size().height;

  bool has_layout_view = false;
  while (box) {
    x += box->Location().X();
    y += box->Location().Y();
    box = box->ContainingBlock();
    if (IsA<LayoutView>(box)) {
      has_layout_view = true;
      break;
    }
  }

  return has_layout_view ? Rect(x.ToFloat(), y.ToFloat(), width.ToFloat(), height.ToFloat()) : Rect();
}

void MTDocument::QuerySelector(const std::string& selectors,
                               QuerySelectorCallback callback_c) {
  WorkerThread::thread().addTask(
      [that = shared_from_this(), selectors, callback_c]() {
        if (that->data_->destroyed_) {
          return;
        }

        Element* element = that->data_->document_->querySelector(
            AtomicString::FromUTF8(selectors.c_str()));
        if (element) {
          auto tag = element->getMscTag();
          auto layout_object = element->GetLayoutObject();
          auto layout_box = To<LayoutBox>(layout_object);
          auto location = layout_box->Location();
          auto size = layout_box->Size();

          callback_c(tag, {location.X().ToFloat(), location.Y().ToFloat()},
                     {size.width.ToFloat(), size.height.ToFloat()});
        }
      });
}

void MTDocument::QuerySelectors(const std::string& selectors,
                                QuerySelectorsCallback callback_c) {
  WorkerThread::thread().addTask(
      [that = shared_from_this(), selectors, callback_c]() {
        if (that->data_->destroyed_) {
          return;
        }
        vector<int> tags;
        vector<std::pair<float, float>> locations;
        vector<std::pair<float, float>> sizes;

        auto elements = that->data_->document_->querySelectorAll(
            AtomicString::FromUTF8(selectors.c_str()));
        tags.reserve(elements->length());
        locations.reserve(elements->length());
        sizes.reserve(elements->length());

        for (unsigned i = 0; i < elements->length(); i++) {
          auto element = elements->item(i);
          if (!element) {
            continue;
          }
          tags.push_back(element->getMscTag());
          auto layout_object = element->GetLayoutObject();
          auto layout_box = To<LayoutBox>(layout_object);
          auto location = layout_box->Location();
          auto size = layout_box->Size();
          locations.push_back({location.X().ToFloat(), location.Y().ToFloat()});
          sizes.push_back({size.width.ToFloat(), size.height.ToFloat()});
        }
        callback_c(std::move(tags), std::move(locations), std::move(sizes));
        delete elements;
      });
}

void MTDocument::FlushUITasksIfNeeded() {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that]() {
    MTDocumentData* data = that->data_;
    if (data->destroyed_) {
      return;
    }

    auto& ui_command_buffer = that->uiCommandBuffer();
    ui_command_buffer.ScheduleCommand(ui_command_buffer.CreateFlushUITasksCommand());
  });
}

void MTDocument::layoutRoot(LayoutReason reason) {
  MSC_TRACE_SCOPED_EVENT(MTDocument::layoutRoot);
  auto that = shared_from_this();
  function<void()> delay_layout;
  {
    std::lock_guard<std::mutex> lock_guard(data_->mutex_);
    if (data_->size_ == Size()) {
      data_->delay_layout_ = [that, reason]() {
        if (that->data_->destroyed_) {
          return;
        }
        that->layoutRoot(reason);
      };
      return;
    }
  }

  WorkerThread::thread().addTask([that, reason]() {
    MTDocumentData* data = that->data_;
    if (data->destroyed_) {
      return;
    }

    auto* document = data->document_.Get();
    auto& engine = document->GetStyleEngine();

    auto token = PerformanceMonitor::createToken();
    PerformanceMonitor::beginEvent(data->perf_id_, token, "layout", 0);
    TRACE_BEGIN(that.get(), "layout", trace::TraceLevel::P1);
    auto& ui_command_buffer = data->ui_command_buffer_;

    // 1. 计算样式
    {
      auto token = PerformanceMonitor::createToken();
      PerformanceMonitor::beginEvent(data->perf_id_, token, "layout.cascade", 0);
      TRACE_BEGIN(that.get(), "layout_cascade", trace::TraceLevel::P1);
      engine.UpdateActiveStyle();
      if (engine.NeedsStyleInvalidation()) {
        engine.InvalidateStyle();
      }
      if (engine.NeedsStyleRecalc()) {
        engine.RecalcStyle();
      }
      PerformanceMonitor::endEvent(data->perf_id_, token, "layout.cascade", 0);
      TRACE_END(that.get(), "layout_cascade");
    }
    // 2. 构建布局树
    {
      if (engine.NeedsLayoutTreeRebuild()) {
        auto token = PerformanceMonitor::createToken();
        PerformanceMonitor::beginEvent(data->perf_id_, token, "layout.updateLayoutTree", 0);
        TRACE_BEGIN(that.get(), "layout_updateLayoutTree", trace::TraceLevel::P1);
        engine.RebuildLayoutTree();

        TRACE_END(that.get(), "layout_updateLayoutTree");
        PerformanceMonitor::endEvent(data->perf_id_, token, "layout.updateLayoutTree", 0);
      }
    }

    // 3. 构建 stacking context
    {
      data->document_->GetLayoutView()->Layer()->UpdateDescendantDependentFlags();
    }

    // 4. 构建渲染树，同时
    // a) 添加创建 View 指令
    // b) 生成删除 View 指令
    // c) 生成修改 View 树结构指令
    {
      document->GetLayoutView()->CreateRenderNodeIfNeeded();
      TraversePaintLayerTree(document->GetLayoutView()->Layer(),
                             [&](PaintLayer* layer, bool *skip_children) {
        auto* layout_box = layer->GetLayoutBox();
        if (!layout_box) {
          *skip_children = true;
          return;
        }
        if (layer->msc_stacking_context_dirty_) {
          layout_box->CreateRenderNodeIfNeeded().updateStackingContextChildren();
          layer->ClearMSCStackingContextDirty();
        }
      });
    }

    // 5. 遍历渲染树，同时
    // a) 添加更新 View 指令
    // b) 添加更新样式指令
    TraverseRenderTree(document->GetLayoutView()->GetRenderNode(),
                       [&](const RenderTreeItem &item, bool* skip_children) {
      item.node->update();
    });

    ui_command_buffer.SendCommands(UICommandBuffer::Opportunity::BeforeLayout);

    // 7. 布局计算
    {
      auto token = PerformanceMonitor::createToken();
      PerformanceMonitor::beginEvent(data->perf_id_, token, "layout.layout", 0);
      TRACE_BEGIN(that.get(), "layout_layout", trace::TraceLevel::P1);
      document->GetLayoutView()->LayoutRoot();
      PerformanceMonitor::endEvent(data->perf_id_, token, "layout.layout", 0);
      TRACE_END(that.get(), "layout_layout");
    }
    // 8. 遍历渲染树，同时
    // a) 添加 View 坐标和大小变更指令
    // b) 添加文本更新指令
    // c) 添加 transform 变更指令
    {
      auto token = PerformanceMonitor::createToken();
      PerformanceMonitor::beginEvent(data->perf_id_, token, "layout.generateResult", 0);
      TRACE_BEGIN(that.get(), "layout_generateResult", trace::TraceLevel::P1);
      TraverseRenderTree(document->GetLayoutView()->GetRenderNode(),
                         [&](const RenderTreeItem &item, bool* skip_children) {
        LayoutBox* box = DynamicTo<LayoutBox>(&item.node->getLayoutBox());
        if (!box) {
          return;
        }

        auto* element = DynamicTo<Element>(box->GetNode());
        if (UNLIKELY(!element)) {
          return;
        }

        if (!box->HasRenderNode()) {
          return;
        }

        Tag tag = box->GetNode()->getMscTag();

        if (box->GeometryChanged() || (element->isText() && box->TextChanged())) {
          RenderNode::Rect rect = GetFinalRect(box);

          bool frame_is_changed = rect != item.node->getRect();
          if (frame_is_changed) {
            item.node->setRect(std::move(rect));
          }

          if (frame_is_changed || (element->isText() && box->TextChanged())) {
            if (element->isText()) {
              auto& text_measurer = box->GetNode()->GetDocument().GetMTDocument()->textMeasurer();
              auto* result = text_measurer.getResult(tag);
              auto* style = box->Style();
              const void* string_data = result ? result->getStringData() : nullptr;
              auto command = ui_command_buffer
                .updateTextCommand(tag,
                                   Device::gridAlignedValue(rect.x.ToFloat()),
                                   Device::gridAlignedValue(rect.y.ToFloat()),
                                   Device::gridAlignedValue(rect.width.ToFloat()),
                                   Device::gridAlignedValue(rect.height.ToFloat()),
                                   Device::gridAlignedValue(style->BorderTopWidth(), true)
                                    + Device::gridAlignedValue(style->PaddingTop().Value()),
                                   Device::gridAlignedValue(style->BorderLeftWidth(), true)
                                    + Device::gridAlignedValue(style->PaddingLeft().Value()),
                                   Device::gridAlignedValue(style->BorderBottomWidth(), true)
                                    + Device::gridAlignedValue(style->PaddingBottom().Value()),
                                   Device::gridAlignedValue(style->BorderRightWidth(), true)
                                    + Device::gridAlignedValue(style->PaddingRight().Value()),
                                   RetainTextData(string_data));
              ui_command_buffer.ScheduleCommand(command);
            } else {
              auto command = ui_command_buffer.updateViewFrameCommand(tag,
                                                                      Device::gridAlignedValue(rect.x.ToFloat()),
                                                                      Device::gridAlignedValue(rect.y.ToFloat()),
                                                                      Device::gridAlignedValue(rect.width.ToFloat()),
                                                                      Device::gridAlignedValue(rect.height.ToFloat()),
                                                                      box->Hidden());
              ui_command_buffer.ScheduleCommand(command);
            }
          }

          box->SetGeometryChanged(false, false);
          box->SetTextChanged(false);
        }

        if (box->TransformChanged()) {
          RenderNode* render_node = box->GetRenderNode();
          const auto& rect = item.node->getRect();
          auto transforms = ConvertTransform(item.node->getLayoutBox().Style()->Transform(), rect);
          if (transforms) {
            ui_command_buffer.ScheduleCommand(ui_command_buffer.updateViewTransformCommand(tag, transforms));
          } else {
            if (render_node && render_node->GetHasTransform()) {
              ui_command_buffer.ScheduleCommand(ui_command_buffer.updateViewTransformCommand(tag, nullptr));
            }
          }
          if (render_node) {
            render_node->SetHasTransform(!!transforms);
          }
          box->SetTransformChanged(false);
        }
      });

      data->text_measurer_.clearResults();
      TRACE_END(that.get(), "layout_generateResult");
      PerformanceMonitor::endEvent(data->perf_id_, token, "layout.generateResult", 0);
    }

    ui_command_buffer.ScheduleCommand(ui_command_buffer.batchDidFinishCommand(reason));
    ui_command_buffer.SendCommands(UICommandBuffer::Opportunity::AfterLayout);

    ui_command_buffer.SetInBatch(false);
    ui_command_buffer.SendCommands(UICommandBuffer::Opportunity::AfterBatch);

//    PrintDomTree(document);
//    PrintLayoutTree(document->GetLayoutView());
//    PrintLayerTree(*document->GetLayoutView()->Layer());
//    PrintStackingTree(*document->GetLayoutView()->Layer());
//    PrintRenderTree(*document->GetLayoutView()->GetRenderNode());

    PerformanceMonitor::endEvent(data->perf_id_, token, "layout", 0);
    TRACE_END(that.get(), "layout");
  });
}

void MTDocument::PostCallbackToService(const msc::JSCallbackInfo& callback_info,
                                       msc::JSCallable::Setter setter) {
  if (!enable_native_dom_) {
    return;
  }
  auto task = new std::function<void()>([registry = this->document_registry_,
                                         page_id = this->pageId_, callback_info,
                                         setter = std::move(setter)]() mutable {
    auto document_registry = registry.lock();
    if (!document_registry) {
      MSC_RENDERER_LOG_ERROR(
          "MTDocument::PostCallbackToService find no document registry");
      return;
    }
    auto dom_document = document_registry->getDOMDocument(page_id);
    if (!dom_document) {
      MSC_RENDERER_LOG_ERROR(
          "MTDocument::PostCallbackToService find no DOM document for page "
          "%d",
          page_id);
      return;
    }
    dom_document->ExecuteCallback(callback_info, std::move(setter));
  });
  if (!this->PostTaskToLogicTaskRunner(task)) {
    delete task;
  }
}

void MTDocument::PostEventToService(int elemId, const std::shared_ptr<msc::native_dom::Event>& event) {
  if (!enable_native_dom_) {
    return;
  }
  auto task = new std::function<void()>(
                                        [that = shared_from_this(),
                                         event = std::move(event),
                                         elemId]() mutable {
                                             auto document_registry = that->document_registry_.lock();
                                               if  (document_registry) {
                                                   auto domDocument = document_registry->getDOMDocument(that->pageId_);
                                                   if (domDocument) {
                                                       domDocument->FireEvent(elemId, event, nullptr, false);
                                                   } else {
                                                       MSC_RENDERER_LOG_ERROR("MTDocument::fireEvent cannot find DOM document for pageId: %d", that->pageId_);
                                                   }
                                               } else {
                                                   MSC_RENDERER_LOG_ERROR("MTDocument::fireEvent cannot find document registry");
                                               }
                                         });
  if (!this->PostTaskToLogicTaskRunner(task)) {
    delete task;
  }
}

void MTDocument::fireEvent(int elemId, const std::shared_ptr<msc::native_dom::EventData> &event_data) {
  const std::string &real_event_name = msc::native_dom::EventUtil::GetMappedEventName(event_data->GetEventName());
  if (real_event_name.size()) {
    event_data->SetEventName(real_event_name);
  }
  
  std::shared_ptr<msc::native_dom::Event> event = nullptr;
  switch (event_data->GetEventCategory()) {
    case msc::native_dom::EventData::EventCategory::TOUCH:
      event = std::make_shared<msc::native_dom::TouchEvent>(*event_data);
      break;
    case msc::native_dom::EventData::EventCategory::TAP:
      event = std::make_shared<msc::native_dom::TapEvent>(*event_data);
      break;
    case msc::native_dom::EventData::EventCategory::COMPONENT:
      event = std::make_shared<msc::native_dom::ComponentEvent>(*event_data);
      break;
    default:
      event = std::make_shared<msc::native_dom::Event>(*event_data);
      break;
  }
  
  auto func = [that = shared_from_this(),
               event = event,
               elemId]() {
    if (that->data_->destroyed_) {
      return;
    }
    
    that->InterceptEventInBlinkThread(elemId, event);
    that->PostEventToService(elemId, std::move(event));
  };
  
  WorkerThread::thread().addTask(std::move(func));
}

void MTDocument::InterceptEventInBlinkThread(int elemId, const std::shared_ptr<msc::native_dom::Event> &event) {
//  if (event->typeName() == "scroll") {
//    auto props = event->detail();
//    double scroll_left = props->getProp("scrollLeft").numberValue();
//    double scroll_top = props->getProp("scrollTop").numberValue();
//    SetMscScrollViewContentOffset(scroll_left, scroll_top, elemId);
//  }
}

void MTDocument::evaluateScript(const std::string& script, const std::string& source) {
  if (data_->js_engine_) {
    auto script_buffer = std::make_shared<facebook::jsi::StringBuffer>(std::move(script));
    data_->js_engine_->EvaluateJavaScript(script_buffer, source);
  }
}

void MTDocument::WXSSetLoadScriptCallback(std::function<void(MTDocument* document, const std::string&)> callback) {
  data_->wxs_load_script_callback_ = callback;
}

void MTDocument::WXSSetTransportCallback(std::function<void(const std::string&)> callback) {
  data_->wxs_transport_callback_ = callback;
}

void MTDocument::WXSLoadScript(const std::string& script_name) {
  if (data_->wxs_load_script_callback_) {
    data_->wxs_load_script_callback_(this, script_name);
  }
}

void MTDocument::WXSTriggerEvent(const std::string& name, Tag target_tag, const std::shared_ptr<const Props>& event) {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, moved_name = std::move(name), target_tag, moved_event = std::move(event)]() {
    if (that->data_->destroyed_) {
      return;
    }

    Element* element = DynamicTo<Element>(that->getNode(target_tag));
    if (!element) {
      return;
    }

    bool is_bubble_event = false; // 是否是冒泡事件
    bool is_composed = false; // 是否穿透组件
    if (moved_event->getProp("isBubbleEvent").isA<PropValueType::Number>()) {
      is_bubble_event = moved_event->getProp("isBubbleEvent").numberValue();
    }
    if (moved_event->getProp("isComposed").isA<PropValueType::Number>()) {
      is_composed = moved_event->getProp("isComposed").numberValue();
    }

    while (true) {
      // 获取组件边界
      Tag target_owner_tag = 1;
      if (!is_composed) {
        if (element->getProp("ownerMscTag").isA<PropValueType::Number>()) {
          target_owner_tag = element->getProp("ownerMscTag").numberValue();
        } else {
          Element* owner_element = that->findOwner(element);
          if (owner_element) {
            target_owner_tag = owner_element->getMscTag();
          }
        }
      }

      Element* target = nullptr;
      std::string event_func_name;
      while (element) {
        // 每次向上查找监听时间的元素时，检查组件边界
        if (!is_composed && element->getMscTag() == target_owner_tag) {
          break;
        }

        const auto& wxs_props = element->getProp("wxsProps");
        if (wxs_props.isA<PropValueType::Dictionary>()) {
          const PropValueType::Dictionary& wxs_props_dict = wxs_props.dictionaryValue();
          auto iter = wxs_props_dict.find(moved_name);
          if (iter != wxs_props_dict.end() && iter->second.isA<PropValueType::String>()) {
            target = element;
            event_func_name = iter->second.stringValue();
            break;
          }
        }

        element = DynamicTo<Element>(element->parentNode());
      }
      if (!target) {
        break;
      }

      PropsBuilder builder;
      if (moved_event->getProp("touches").isA<PropValueType::Array>()) {
        builder.setProp("touches", moved_event->getProp("touches"));
      }
      if (moved_event->getProp("eventData").isA<PropValueType::Dictionary>()) {
        builder.setProp("eventData", moved_event->getProp("eventData"));
      }
      bool bubble = that->WXSTriggerEventInternal("execWxsEvent",
                                                  target,
                                                  event_func_name,
                                                  builder.getProps());
      if (!is_bubble_event || !bubble) {
        break;
      }

      element = DynamicTo<Element>(element->parentNode());

      // 冒泡时，检查组件边界
      if (element) {
        if (!is_composed && element->getMscTag() == target_owner_tag) {
          break;
        }
      }
    }

    that->layoutRoot(LayoutReason::WXSSetStyle);
  });
}

void MTDocument::WXSTriggerPropChangeEvent(const std::string& prop_name,
                                           Tag target_tag,
                                           const std::shared_ptr<const Props>& args) {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, moved_prop_name = std::move(prop_name), target_tag,
                                  moved_args = std::move(args)]() {
    if (that->data_->destroyed_) {
      return;
    }

    Element* element = DynamicTo<Element>(that->getNode(target_tag));
    if (!element) {
      return;
    }

    std::string event_func_name;
    std::string changed_prop_name = "change:" + moved_prop_name;
    const auto& wxs_props = element->getProp("wxsProps");
    if (wxs_props.isA<PropValueType::Dictionary>()) {
      const PropValueType::Dictionary& wxs_props_dict = wxs_props.dictionaryValue();
      auto iter = wxs_props_dict.find(changed_prop_name);
      if (iter != wxs_props_dict.end() && iter->second.isA<PropValueType::String>()) {
        event_func_name = iter->second.stringValue();
      }
    }
    if (event_func_name.length() <= 0) {
      return;
    }

    that->WXSTriggerEventInternal("execWxsPropObserver", element, event_func_name, moved_args);
  });
}

void MTDocument::WXSTrasport(const std::string& args) {
  if (data_->wxs_transport_callback_) {
    data_->wxs_transport_callback_(args);
  }
}

#pragma mark - private

const Config& MTDocument::config() const {
  return data_->config_;
}

class TextMeasurer& MTDocument::textMeasurer() const {
  return data_->text_measurer_;
}

UICommandBuffer& MTDocument::uiCommandBuffer() const {
  return data_->ui_command_buffer_;
}

Node* MTDocument::getNode(Tag tag) {
  auto iter = data_->tag2node_.find(tag);
  if (iter == data_->tag2node_.end()) {
    return nullptr;
  }
  return iter->second;
}

Node* MTDocument::getChildNode(Node *parent_node, int index) {
  auto *child = To<Element>(parent_node->firstChild());
  while (index > 0 && child) {
    child = To<Element>(child->nextSibling());
    index--;
  }
  return child;
}

void MTDocument::addNodeInternal(Tag tag, Node *node) {
  data_->tag2node_.insert(make_pair(tag, node));
}

void MTDocument::removeNodeInternal(Tag tag, vector<int> &removed_tags) {
  auto iter = data_->tag2node_.find(tag);
  if (iter == data_->tag2node_.end()) {
    return;
  }

  auto sibling = iter->second->firstChild();
  while (sibling) {
    removeNodeInternal(sibling->getMscTag(), removed_tags);
    sibling = sibling->nextSibling();
  }

  LayoutBox* layout_box = iter->second->GetLayoutBox();
  if (layout_box && layout_box->HasRenderNode()) {
    removed_tags.push_back(tag);
  }
  data_->tag2node_.erase(iter);
}

void MTDocument::preCalculateText(Element* element) {
  if (!config().enable_text_pre_calculate) {
    return;
  }

  if (element->GetLayoutBox()) {
    return;
  }

  auto& engine = data_->document_for_text_pre_calculate_->GetStyleEngine();
  Document* document = data_->document_for_text_pre_calculate_;

  document->ParserAppendChild(element);

  engine.UpdateStyleRecalcRoot(nullptr, element);
  engine.UpdateLayoutTreeRebuildRoot(nullptr, element);

  engine.UpdateActiveStyle();
  engine.RecalcStyle();
  engine.RebuildLayoutTree();
  data_->text_measurer_.measure(element->getMscTag(), element->GetLayoutBox(), kIndefiniteSize);

  document->ParserRemoveChild(*element);
}

void MTDocument::loadWXSEngine() {
  if (!data_->js_engine_) {
    data_->js_engine_ = std::make_shared<JSEngine>(shared_from_this());
    data_->js_engine_->Init();
  }

  WXSLoadScript("wxs_app");
  WXSLoadScript("wxs_runtime");

  data_->wxs_ready_ = true;
}

bool MTDocument::WXSTriggerEventInternal(const std::string& action,
                                         Element* target,
                                         std::string event_func_name,
                                         const std::shared_ptr<const Props>& args) {
  PropsBuilder builder;
  builder.setProp("pageId", config().page_id);
  builder.setProp("pagePath", config().page_path);
  builder.setProp("viewId", (PropValueType::Number)target->getMscTag());
  Tag owner_msc_tag = 1;
  if (target->getProp("ownerMscTag").isA<PropValueType::Number>()) {
    owner_msc_tag = target->getProp("ownerMscTag").numberValue();
  }
  builder.setProp("ownerViewId", owner_msc_tag);
  builder.setProp("eventFunc", event_func_name);
  builder.setProp("propFunc", event_func_name);

  auto& wxs_func_path = target->getProp("wxsFuncPath");
  std::string wxs_func_path_str;
  if (wxs_func_path.isA<PropValueType::String>()) {
    wxs_func_path_str = wxs_func_path.stringValue();
  } else if (target->getProp("ownerMscTag").numberValue() == 1) {
    wxs_func_path_str = config().page_path;
  } else {
    wxs_func_path_str = target->componentName().Utf8();
  }
  builder.setProp("funcPath", wxs_func_path_str);

  args->forEach([&builder](const std::string& key, const PropValue& value) {
    builder.setProp(key, value);
  });

  auto& props = builder.getProps();

  auto& js_engine = data_->js_engine_;
  PropValueType::Array arguments;
  arguments.push_back("WxsEmitter");
  arguments.push_back(action);
  arguments.push_back(propsToJson(props));
  auto& ret= js_engine->InvokeJSCall("WxsJSBridge", "invoke", arguments);

  if (ret.isA<PropValueType::String>()) {
    return ret.stringValue() == "true" || ret.stringValue() == "1";
  }

  return true;
}

Element* MTDocument::findOwner(Element* element) {
  while (element) {
    if (element->parentNode()->IsDocumentNode()) {
      return element;
    }

    auto& is = element->getProp("is");
    if (is.isA<PropValueType::String>()) {
      auto& is_str = is.stringValue();
      if (is_str.length() > 0 && is_str[0] == '/') {
        return element;
      }
    }

    element = DynamicTo<Element>(element->parentNode());
  }
  return nullptr;
}

#ifndef __APPLE__
void MTDocument::performBlockOnBlinkThread(std::function<void()> block) {
  WorkerThread::thread().addTask(block);
}

std::pair<uint8_t*, size_t> MTDocument::generateResult(flatbuffers::FlatBufferBuilder &builder, const blink::mt::UICommands &buffer){
  auto getter = [this](int tag) -> std::shared_ptr<const NativeStyle> {
      auto iter = data_->tag2NativeStyle_.find(tag);
      if (iter == data_->tag2NativeStyle_.end()) {
        return nullptr;
      }
      return iter->second;
  };
  auto setter = [this](int tag, std::shared_ptr<const NativeStyle> item) {
      data_->tag2NativeStyle_.insert_or_assign(tag, item);
  };
  auto token = PerformanceMonitor::createToken();
  PerformanceMonitor::beginEvent(data_->perf_id_, token, "layout.json", 0);
  TRACE_BEGIN(this, "layout_json", trace::TraceLevel::P1);
  std::pair<uint8_t*, size_t> result = blink::mt::GetResult(builder, buffer, data_->size_.density, getter, setter);
  TRACE_END(this, "layout_json");
  PerformanceMonitor::endEvent(data_->perf_id_, token, "layout.json", 0);
  return result;
}

#endif

void MTDocument::SetMscScrollViewContentOffset(double x, double y, long tag) {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, x, y, tag]() {
    auto itr = that->data_->tag2node_.find(tag);
    if (itr == that->data_->tag2node_.end()) {
      return;
    }
    Node* node = itr->second;
    auto layout_object = node->GetLayoutObject();
    auto layout_box = To<LayoutBox>(layout_object);

    layout_box->SetMscScrolledContentOffset(x, y);

    // trigger intersection observer
    auto document = that->data_->document_.Get();
    if (!document) {
      return;
    }
    auto controller = document->GetIntersectionObserverController();
    if (!controller) {
      return;
    }
    controller->ComputeIntersections(
        IntersectionObservation::kExplicitRootObserversNeedUpdate,
        IntersectionGeometry::kInfiniteScrollDelta);
    controller->DeliverNotifications(IntersectionObserver::kPostTaskToDeliver);
  });
}

void MTDocument::CreateKeyframesAnimationEnhanced(
    const std::shared_ptr<blink::mt::AnimationProperties>& properties,
    msc::JSCallbackInfo callback_info) {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, properties, callback_info]() {
    if (that->data_->destroyed_) {
      return;
    }

    auto& tags = properties->props->getProp("tags").arrayValue();
    auto& keyframes = properties->props->getProp("keyframes").arrayValue();
    auto duration = properties->props->getProp("duration").numberValue();

    auto command =
        that->data_->ui_command_buffer_.createKeyframesAnimationCommand(
            std::move(tags), std::move(keyframes), duration,
            [that, callback_info]() {
              if (callback_info.is_valid) {
                that->PostCallbackToService(callback_info, nullptr);
              }
            });

    that->data_->ui_command_buffer_.ScheduleCommand(command);
  });
}

void MTDocument::ClearKeyframesAnimationEnhanced(
    const std::shared_ptr<blink::mt::ClearAnimationProperties>& properties,
                                                 msc::JSCallbackInfo callback_info) {
  auto that = shared_from_this();
  WorkerThread::thread().addTask([that, properties, callback_info]() {
    if (that->data_->destroyed_) {
      return;
    }
    
    auto& tags = properties->props->getProp("tags").arrayValue();
    auto& options = properties->props->getProp("options");
    
    auto command =
    that->data_->ui_command_buffer_.clearKeyframesAnimationCommand(
                                                                   tags, options, [that, properties, callback_info]() {
                                                                     if (callback_info.is_valid) {
                                                                       that->PostCallbackToService(callback_info, nullptr);
                                                                     }
                                                                   });
    
    that->data_->ui_command_buffer_.ScheduleCommand(command);
  });
}
  
// TraceRecorder相关方法实现
void MTDocument::setTraceRecorderPtr(long traceRecorderPtr) {
  // 检查指针是否为有效值（非0）
  if (traceRecorderPtr != 0) {
    trace_recorder_ptr_ = reinterpret_cast<trace::TraceRecorder*>(traceRecorderPtr);
  } else {
    // 如果传入的指针为0或其他非法值，设置为nullptr
    trace_recorder_ptr_ = nullptr;
  }
}

trace::TraceRecorder* MTDocument::getTraceRecorder() const {
  return trace_recorder_ptr_;
}

}; // namespace mt

}; // namespace blink
