//
// Created by lilei118 on 2025/2/7.
//

#ifndef MT_CANVAS_ANDROID_TRACE_H
#define MT_CANVAS_ANDROID_TRACE_H

namespace blink {

    namespace atrace {

// 函数类型定义
        typedef void* (*fp_ATrace_beginSection)(const char* sectionName);
        typedef void* (*fp_ATrace_endSection)(void);
        typedef bool* (*fp_ATrace_isEnabled)(void);

// 函数声明
        void InitTrace();
        void BeginTrace(const char* sectionName);
        void EndTrace();

    }  // namespace atrace

}  // namespace blink

#endif  // MT_CANVAS_ANDROID_TRACE_H